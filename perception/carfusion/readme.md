日期：2025.09.09
修改人：韩双全
版本号：4.0.1
修改项：tracking
1.适配红旗HS5-160

日期：2025.09.08
修改人：韩双全
版本号：4.0.0
修改项：tracking
1.迁移ROS2

日期：2025.07.03
修改人：韩双全
版本号：3.4.6
修改项：tracking
1.V2I取消车端使用基站检测类型

日期：2025.05.23
修改人：韩双全
版本号：3.4.5
修改项：tracking
1.自车加入跟踪与OBU匹配
2.取消二次匹配关于速度和位置计算航向角约束

日期：2025.02.17
修改人：韩双全
版本号：3.4.4
修改项：tracking
1.添加kalman更新后速度优化时速度计算角度的范围限制
2.优化速度修正方法：添加动态阈值调整

日期：2025.01.11
修改人：韩双全
版本号：3.4.3
修改项：tracking
1.解决非时延导致的OBU删除问题
2.解决云端目标的暂留问题

日期：2024.11.29
修改人：韩双全
版本号：3.4.2
修改项：tracking
1.添加耗时统计：检测目标接收-ROS、跟踪目标发送-ROS、V2I发送-ROS时间戳

日期：2024.10.30
修改人：韩双全
版本号：3.4.1
修改项：tracking
1.添加车-路所有目标数与耗时的统计
2.针对路口车路融合上的目标在类别更新时使用最多类别的长宽

日期：2024.10.21
修改人：韩双全
版本号：3.4.0
修改项：
1.tracking：修正kalman拷贝构造函数参数、二次匹配速度限制调整、添加文档写入及参数配置
2.V2I部分：限制发送到PAD的条件

日期：2024.09.03
修改人：韩双全
版本号：3.3.2
修改项：
1.跟踪部分处理：二次匹配机动车与聚类不匹配（保证速度准确性）、添加cone2car的类型转变、机动车航向角优化使用均值更新替换中值更新、发布跟踪时补充在基站范围的融合状态赋值
6.V2I部分：融合匹配时禁止路端与车端聚类匹配、未匹配的OBU目标融合类型赋值、添加V2I单独显示的话题、屏蔽僵尸车的处理、限制V2I发布的条件及发送到PAD的条件

日期：2024.08.10
修改人：韩双全
版本号：3.3.1
修改项：
1.使用参数配置UTMCode
2.OBU检测的自车目标滤除

日期：2024.08.05
修改人：韩双全
版本号：3.3.0
修改项：
1.特定bicycle\tricycle2car的类别跳变后，优化类别及速度更新
2.针对匹配错误的radar速度更新，更新后修正速度
3.若跟踪角度错误，拆分跟踪计算角度和反向检测角度的判断更新跟踪后角度

日期：2024.07.26
修改人：韩双全
版本号：3.2.17
修改项：
1.调整频率，调整回调和主函数执行顺序
2.添加检测丢帧判断

日期：2024.07.25
修改人：韩双全
版本号：3.2.16
修改项：
1.细化跟踪-OBU融合标志位更新
2.修正OBU目标的mergeZombieObject

日期：2024.07.24
修改人：韩双全
版本号：3.2.15
修改项：
1.感知目标接收耗时显示
2.注释OBU目标的mergeZombieObject

日期：2024.07.23
修改人：韩双全
版本号：3.2.14
修改项：
1.放宽非机动车和聚类目标建立跟踪目标限制
2.在保证跟踪航向角大致准确的情况下，使用与检测航向角余弦距离的航向角更新跟踪航向角

日期：2024.07.22
修改人：韩双全
版本号：3.2.13
修改项：
1.取消行人和自行车的类别合并
2.发布跟踪的聚类目标的点云
3.配置文件取消使用高精地图滤除目标

日期：2024.07.19
修改人：韩双全
版本号：3.2.12
修改项：
1.更改ROS频率
2.优化机动车初始轨迹的保存更新策略

日期：2024.07.12
修改人：韩双全
版本号：3.2.11
修改项：
1.v2i融合标志修正

日期：2024.07.11
修改人：韩双全
版本号：3.2.10
修改项：
1.跟踪类别更新策略和长宽更新策略
2.优化二次匹配角度限制

日期：2024.07.10
修改人：韩双全
版本号：3.2.9
修改项：
1.使用建立跟踪目标条件:添加航向角和距离条件判断
2.调整机动车、非机动车航向角策略

日期：2024.07.09
修改人：韩双全
版本号：3.2.8
修改项：
1.添加建立跟踪目标条件（未使用）：未与前帧未匹配检测得当前帧检测目标
2.更改机动车航向角异常阈值，并优化静止机动车航向角的更新策略

日期：2024.07.08
修改人：韩双全
版本号：3.2.7
修改项：
1.预测航向角约束:当前预测与上一帧跟踪误差大，使用上一帧跟踪航向角

日期：2024.07.05
修改人：韩双全
版本号：3.2.6
修改项：
1.机动车航向角优化:静止航向角反向，运动时修正

日期：2024.07.04
修改人：韩双全
版本号：3.2.5
修改项：
1.dl-cluster匹配的中心点优化

日期：2024.07.04
修改人：韩双全
版本号：3.2.4
修改项：
1.机动车航向角根据速度分别优化

日期：2024.07.04
修改人：韩双全
版本号：3.2.3
修改项：
1.参数转移到参数管理器

日期：2024.07.03
修改人：韩双全
版本号：3.2.2
修改项：
1.更改cout为spdlog日志

日期：2024.07.02
修改人：韩双全
版本号：3.2.1
修改项：
1.解决内存占用增长及内存其他问题

日期：2024.06.27
修改人：韩双全
版本号：3.2.0
修改项：
1.添加log管理器

日期：2024.06.26
修改人：韩双全
版本号：3.1.13
修改项：
1.优化目标长宽限制
2.优化历史航向角保存
3.调整跟踪信息显示

日期：2024.06.25
修改人：韩双全
版本号：3.1.12
修改项：
1.调整跟踪信息显示
2.修正第二次匹配航向角取值

日期：2024.06.25
修改人：韩双全
版本号：3.1.11
修改项：
1.预处理增加低置信度lidar目标滤除
2.添加各小模块耗时统计
3.部分循环使用并行化处理
4.数据关联重复计算、跟踪目标合并条件、机动车发布条件优化
5.跟踪目标更新前速度异常判断优化，添加与上一帧速度对比判断

日期：2024.06.19
修改人：韩双全
版本号：3.1.10
修改项：
1.优化调试显示
2.类内参数的初始化
3.mergeTrackedObjectByIOU使用的参数改为跟踪结果

日期：2024.06.17
修改人：韩双全
版本号：3.1.9
修改项：
1.优化lidar-radar匹配的速度异常造成跟踪目标速度异常问题

日期：2024.06.14
修改人：韩双全
版本号：3.1.8
修改项：
1.引入参数配置类
2.debug使用参数配置类

日期：2024.06.14
修改人：韩双全
版本号：3.1.7
修改项：
1.放开sensorradar输入
2.添加离线lidar-radar匹配成功的radar显示
3.参数传递减少重复计算
4.合并跟踪目标用IOU时添加距离判断
5.kalman更新速度优化：速度计算函数更换为bodyAxis2ENU、删除速度异常判断

日期：2024.06.12
修改人：韩双全
版本号：3.1.6
修改项：
1.更新过程中目标非聚类不进行速度平滑velocitySmooth

日期：2024.06.11
修改人：韩双全
版本号：3.1.5
修改项：
1.数据容器加锁
2.耗时优化：参数传递优化、编译优化
3.跟踪与聚类更新中中心点的优化

日期：2024.06.05
修改人：韩双全
版本号：3.1.4
修改项：
1.车端跟踪：去掉跟踪目标8角点更新，发布时points使用检测多边形点发布；调整跟踪目标合并的IOU阈值；
2.V2I融合：发布时points使用检测多边形点发布

日期：2024.06.04
修改人：韩双全
版本号：3.1.3
修改项：
1.修正lidar-obu匹配对跟踪目标的速度更新（暂未使用）
2.修正跟踪目标的初始速度

日期：2024.06.03
修改人：韩双全
版本号：3.1.2
修改项：
1.KalmanFilter航向角归一化
2.ROS话题订阅优化通信效率
3.去除相机订阅信息
4.类别更新优化及（行人、自行车）混检固定自行车
5.取消lidar-obu匹配使用OBU速度

日期：2024.05.27
修改人：韩双全
版本号：3.1.1
修改项：
1.聚类目标：输入和输出端分别进行航向角优化、输入端长宽进行优化、不更新8角点、聚类目标的发布阈值、跟踪速度初值为0、更新后的速度约束、更新后航向角约束
2.设置在线模式不显示聚类框(用lidarclusterrviz的点云显示聚类目标)
3.添加一帧跟踪后进行障碍物合并
4.优化类别更新

日期：2024.05.16
修改人：韩双全
版本号：3.1.0
修改项：
1.添加高精矢量图滤除非道路目标方法、高精矢量图
2.添加匹配的边界条件处理
3.优化跟踪类别更新、跟踪发布条件、添加预测速度约束、不同交通参与者航向角处理、
5.v2i自车目标删除、优化匹配目标的更新，添加距离匹配、优化发布条件、聚类点代替8角点存储


日期：2024.04.19
修改人：韩双全
版本号：3.0.0
修改项：
1.传感器类数据接入移入commonlibrary
2.预测数据位置平滑


日期：2024.04.15
修改人：韩双全
版本号：2.21
修改项：
1.第二次匹配改为距离匹配
2.车路融合统一ID
3.不同类别优化静止的位置航向角
4.补充Lidar目标类

日期：2024.04.09
修改人：韩双全
版本号：2.20
修改项：
1.添加基站目标和相机的类
2.添加相机的获取接口
3.添加基站目标的融合，优化

日期：2024.03.26
修改人：韩双全
版本号：2.19
修改项：
1.速度平滑修正
2.位置平滑
3.删除无关代码及代码文件

日期：2024.03.25
修改人：韩双全
版本号：2.18
修改项：
1.发布到RVIZ的目标中加入虚拟目标
2.删除无关代码

日期：2024.03.07
修改人：韩双全
版本号：2.17
修改项：
1.更改靖江SLAM区域范围

日期：2024.02.26
修改人：韩双全
版本号：2.16
修改项：
1.添加仿真模式单独发送虚拟目标到下游的模式
2.DL—cluster目标检测适配

日期：2024.02.02
修改人：韩双全
版本号：2.15
修改项：
1.修正虚拟目标的航向角和相对速度
2.修正目标时间同步策略


日期：2024.01.31
修改人：韩双全
版本号：2.15
修改项：
1.解决没有目标造成的跟踪挂掉bug
2.添加v2i的融合
2.添加目标、基站目标、参数管理类

日期：2024.01.24
修改人：韩双全
版本号：2.14
修改项：
1.修正决策用目标的多变形包围框发布

日期：2024.01.23
修改人：韩双全
版本号：2.13
修改项：
1.航向角优化新思路：列出航向角更新情况，其他情况维持上一帧

日期：2024.01.22
修改人：韩双全
版本号：2.12
修改项：
1.添加SLAM的GPS订阅及GPS更新
2.虚实结合功能添加虚拟目标的订阅、预处理和发布
3.跟踪优化：建立跟踪目标的置信度需要达到0.4；类别跳变导致的航向角跳变；航向角优化

日期：2023.12.15
修改人：韩双全
版本号：2.11.2
修改项：
1.修复崩溃bug


日期：2023.12.15
修改人：韩双全
版本号：2.11
修改项：
1.第二次匹配添加类别限制
2.修复横向航向角和航向角旋转问题

日期：2023.12.06
修改人：韩双全
版本号：2.10
修改项：
1.修正跟踪目标缺失bug

日期：2023.11.22
修改人：韩双全
版本号：2.9
修改项：
1.限制距离匹配的距离和类别；
2.耗时优化：跟踪使用优化后的转换函数
3.修正发布车后轴RFU相对速度的bug

日期：2023.11.21
修改人：韩双全
版本号：2.8
修改项：
1.减小radar作为独立目标的范围为横向正负10米
2.优化代码耗时

日期：2023.11.17
修改人：韩双全
版本号：2.7
修改项：
1.耗时优化：
    循环条件赋值优化
    kalman预测的结果不保存
2.跟踪目标删除封装函数



日期：2023.11.17
修改人：韩双全
版本号：2.6
修改项：
1.修正跟踪发布用于决策的目标没有8角点
2.跟踪中删除横向距离大于4.9米并且对地速度小于1的自行车
3.FusionAndTrk文件夹改名为fusiontracking后的头文件及launch替换

日期：2023.11.17
修改人：韩双全
版本号：2.5
修改项：
1.添加跟踪发布用于决策的目标没有多边形点
2.radar目标加入检测结果用于与跟踪结果匹配
3.FusionAndTrk文件夹改名为fusiontracking
4.CMakeLists添加ros install信息


日期：2023.11.16
修改人：韩双全
版本号：2.4
修改项：
1.添加radar目标作为交通参与者的横向距离限制条件
2.修正lidar-radar未融合目标的属性赋值
3.跟踪目标的m_speedSource替换为统一m_value，并删除m_speedSource
4.限制radar目标作为交通参与者的发布和显示条件

日期：2023.11.16
修改人：韩双全
版本号：2.3
修改项：
1.添加radar目标作为交通参与者参数
2.修正未融合目标的value值
3.减小代码耗时
4.修正跟踪目标的高度

日期：2023.11.14
修改人：韩双全
版本号：2.2
修改项：
1.修正目标发送频率不正确的bug
2.简化kalman跟踪代码

日期：2023.11.14
修改人：韩双全
版本号：2.1
修改项：
1.跟踪目标航向角使用正北方向0-360
2.跟踪角度约束到0-360度
3.修正radar融合标志位与类别对应（适配radar修改类别发送后）

日期：2023.11.13
修改人：韩双全
版本号：2.0
修改项：
1.重构融合跟踪：合并为一个节点，使用绝对位置和绝对速度
2.kalmanfilter添加Vx,Vy的修正


日期：2023.11.06
修改人：韩双全
版本号：6.14
修改项：
1.添加速度修正（未使用）
2.添加融合跟踪合并节点

日期：2023.10.25
修改人：韩双全
版本号：6.13
修改项：
1.添加KalmanTracker类通过参数获取cityUTMCode；
2.使用参数判断分析目标还是评估跟踪情况


日期：2023.10.11
修改人：刘明旭
版本号：6.12
修改项：
1.更改发送到的APP的条件；

日期：2023.10.10
修改人：刘明旭
版本号：6.11
修改项：
1.修正航向角计算；


日期：2023.10.10
修改人：刘明旭
版本号：6.10
修改项：
1.修正航向角计算；
2.限制发送到RVIZ、app的显示距离


日期：2023.10.09
修改人：韩双全
版本号：6.9
修改项：
1.修正改成lidar检测转到车后轴的历史轨迹UTM、航向角计算；
2.取消注释更新8角点；

日期：2023.10.08
修改人：韩双全
版本号：6.8
修改项：
1.添加距离基站范围判断，超出范围，lidar-obu融合标志不保持；
2.取消注释更新8角点；


日期：2023.10.07
修改人：韩双全
版本号：6.7
修改项：
1.减少订阅融合话题缓存大小到1；
2.预测、更新航向角约束到0-360度；
3.优化已经初始化并且不是新建跟踪链目标的结果发布
4.提高订阅发布频率，20提升到50

日期：2023.09.26
修改人：韩双全
版本号：6.6
修改项：
1.保持lidar-obu融合目标value值固定；
2.修正静止目标保持长宽的判断条件；
3.修正跟踪显示的目标VALUE值

日期：2023.09.25
修改人：韩双全
版本号：6.5
修改项：
1.类别变化时保持跟踪中心点不变；

日期：2023.09.20
修改人：韩双全
版本号：6.4
修改项：
1.优化航向角修正；

日期：2023.09.09
修改人：韩双全
版本号：6.3
修改项：
1.添加历史轨迹的长宽、类别、航向角
2.添加长宽、类别、航向角修正；

日期：2023.08.30
修改人：韩双全
版本号：6.2
修改项：
1.添加车辆中心到车辆后轴的参数
2.修正UTMCode的参数；

日期：2023.08.21
修改人：韩双全
版本号：6.1
修改项：
1.跟踪将IOU计算、匈牙利匹配、公共定义等代码替换为common库中的代码，并进行输入输出适配

日期：2023.08.17
修改人：韩双全
版本号：6.0
修改项：
1.删除无用代码、、删除聚类相关函数
2.根据融合情况调整kalman参数
3.common库函数替换类内函数
4.目标长宽由融合节点统一，代码所用长宽统一


日期：2023.08.09
修改人：韩双全
版本号：6.0
修改项：
1.删除无用代码、、删除聚类相关函数
2.根据融合情况调整kalman参数
3.common库函数替换类内函数
4.目标长宽由融合节点统一，代码所用长宽统一

日期：2023.08.08
修改人：韩双全
版本号：5.1
修改项：
1.添加后轴中心XY坐标转经纬度；
2.融合的检测目标根据置信度降序排序；
3.添加跟踪频率

日期：2023
修改人：韩双全
版本号：5.0
修改项：
1.两辆车统一代码；



日期：2023.06.06
修改人：韩双全
版本号：4.0
修改项：
1.跟踪节点使用航向角和理论速度约束横向速度（在正负1m/s范围内，横向96.769%，纵向95.652%，使用强约束，横向100%）；
2.跟踪发布8角点用于系统RVIZ显示，多边形用于决策；
3.跟踪节点预测6帧，车辆显示约束4帧，其他类型显示调整，kalman参数调整；
4.计算轨迹速度imu2lidar外参调整为红旗2；
5.跟踪显示放到发布后
6.删除无用msg,添加目标历史预测、车道线相关msg
7.模块根据msg删除无用代码,删除原发布历史轨迹话题代码
8.driver删除jmcamera


日期：2023.05.24
修改人：韩双全
版本号：3.1
修改项：
    1.添加耗时话题发布；
    2.不发布8角点，只发布多边形
    3.调整kalman-R值中速度参数
    
    
日期：2023.04.23
修改人：韩双全
版本号：3.0
修改项：
    1.跟踪节点添加ObjectID；
    2.调整kalman参数；


日期：2023.04.13
修改人：韩双全
版本号：2.0
修改项：
    1.添加跟踪节点；
    2.修正航向角信息；更新8角点信息；

