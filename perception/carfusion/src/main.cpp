/*
 * @Description: 
 * @Version: 2.0
 * @Autor: shuangquan han
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-09-18 11:22:31
 */
/********************************************************************************
* @author: shuangquan han
* @date: 2023/6/21 下午4:22
* @version: 1.0
* @description: 
********************************************************************************/

#include "carfusion.h"

#ifdef ROS1_FOUND
int main(int argc, char **argv){
	ros::init(argc, argv, "carfusion");
	ros::param::set("/version/carfusion","2025-09-8-v4.0.0");
	
	ros::NodeHandle nh;
	CarFusion carfusion(nh);
	carfusion.run();
	
	return 0;
}
#endif
#ifdef ROS2_FOUND
int main(int argc, char **argv){ 
	rclcpp::init(argc, argv);
	auto node = rclcpp::Node::make_shared("carfusion");
	node->declare_parameter("/version/carfusion", "2025-09-8-v4.0.0");
	CarFusion carfusion(node);
	carfusion.run();
	return 0;
}
#endif