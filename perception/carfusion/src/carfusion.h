/********************************************************************************
* @author: shuangquan han
* @date: 2023/6/21 下午5:48
* @version: 1.0
* @description: 
********************************************************************************/


#ifndef SRC_FUSIONTRACKING_H
#define SRC_FUSIONTRACKING_H

#include <iostream>
#include <mutex>
#include <deque>
#include <vector>
#include <thread>

#include <eigen3/Eigen/Dense>
#include <spdlog/spdlog.h>

#ifdef ROS1_FOUND
#include <ros/ros.h>
#include <pcl_ros/point_cloud.h>
#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>
#include "common_msgs/sensorobject.h"
#include "common_msgs/sensorobjects.h"
#include "common_msgs/sensorgps.h"
#include "common_msgs/cloudpant.h"
#include "common_msgs/cloudpants.h"
#include "common_msgs/elapsedtime.h"
#include "common_msgs/decisionbehavior.h"

#include "../../commonlibrary/src/common.h"
#include "../../commonlibrary/src/Hungarian.h"
#include "../../commonlibrary/src/getiou.h"
#include "../../commonlibrary/src/computeAssignmentMatrix.h"
#include "../../commonlibrary/src/coordinateTransformation/wgs84_utm.h"
#include "../../commonlibrary/src/coordinateTransformation/sensorAxisTransformation/sensorAxisTransformation.h" //从FusionAndTrk/src目录开始算再去找头文件路径
#include "../../commonlibrary/src/visualize/sensorobjectsviewer.h"
#include "../../commonlibrary/src/visualize/viewer.h"
#include "../../commonlibrary/src/configManager/configManager.h"
#include "../../commonlibrary/src/sensorobjects/cameraobjects.h"
#include "../../commonlibrary/src/logger/logger.h"
#include "../../commonlibrary/src/fileWriter/fileWriter.h"


using namespace std;
typedef long int64;

const static float   ANGLE_FACTOR = 0.017453; //m_pi/180.0

class CarFusion {
public:
	CarFusion(ros::NodeHandle nh);
	~CarFusion();

	ros::NodeHandle m_nh;
	void run();
	void fusion();
	void objectFusion();
	void publishFusionObject();
	
	////// 订阅话题
	ros::Subscriber sub_apolloObjects;
	ros::Subscriber sub_gps;
	ros::Subscriber sub_cloudobjects;

	////// 发布话题
	ros::Publisher pub_fusionObject;
	ros::Publisher pub_lidarObjectBBX;
	ros::Publisher pub_fusionObjectBBX;
	
	ros::Publisher pub_fusiontrackingElapsedtime;
	ros::Publisher pub_cloudObjectsBBX;

	////// 回调函数

	void SubCallback_apolloObjects(const common_msgs::sensorobjects::ConstPtr &msg);
	void SubCallback_gps(const common_msgs::sensorgps::ConstPtr &msg);
	void SubCallback_cloudpants(const common_msgs::cloudpants::ConstPtr &msg);
	void sensorTimeSynchro();
	
	//////功能函数
	template <typename T>
	void timeSynchro(std::deque<T>& msgDeque,const int64& curObjectFrameStamp, const int synchroFlag);
	void LatLonToLocalXY(double lon_car, double lat_car, float& x, float& y);
	
	void sensorobjectsBoxShow(const common_msgs::sensorobjects &msg_source, const int& sensorType);
	void apolloObjectsPreprocess();
	void cloudpantsPreprocess(const common_msgs::cloudpants& cloudpants, common_msgs::sensorobjects& cloudObjects);
	int transCloudObjectType2CameraDetectionType(const int& cloudObjectType);

	
private:
	Common m_common;
	wgs84_utm m_wgs84Utm;
	SensorAxisTransformation m_sensorAxisTransformer;
	boost::shared_ptr<ConfigManager::ConfigManager> m_pConfigManager;
	std::shared_ptr<Logger> m_pcLogger;
	std::shared_ptr<spdlog::logger> m_pLogger;
	boost::shared_ptr<FileWriter> m_pTimeUseFileWriter;

	//外参
	float m_lidar2carBackRFU_X;
	float m_lidar2carBackRFU_Y;
	float m_lidar2carBackRFU_Z;
	
	float m_lidarFLU2carBackRFU_rollDegree;
	float m_lidarFLU2carBackRFU_pitchDegree;
	float m_lidarFLU2carBackRFU_yawDegree;
	
	float m_lidarRFU2carBackRFU_rollDegree;
	float m_lidarRFU2carBackRFU_pitchDegree;
	float m_lidarRFU2carBackRFU_yawDegree;
	
	Eigen::Vector3d m_lidarRFU2carBackRFU_eulerXYZDegree;
	Eigen::Vector3d m_lidarFLU2carBackRFU_eulerXYZDegree;
	Eigen::Vector3d m_lidar2carBackRFU_translation;

	
	//// 当前帧数据
	common_msgs::sensorobjects m_lidarMsg;      //lidar data
	common_msgs::sensorgps     m_gpsMsg ;       // gps data
	common_msgs::cloudpants m_cloudpantsMsg; //cloudpants data
	common_msgs::sensorobjects m_cloudObjectMsg; //cloudpants data
	common_msgs::sensorobjects m_fusionObjectMsg;
	
	std::vector<double> m_selfCarSpeed;
	std::vector<double> m_selfCarUTMPosition; //三维
	
	//锁变量
	std::mutex m_lidarMutex;
	std::mutex m_gpsMutex;
	std::mutex m_cloudpantsMutex;
	
	////数据容器
	std::deque<common_msgs::sensorobjects> m_lidarMsgDeque_;
	std::deque<common_msgs::sensorgps> m_gpsMsgDeque_;
	std::deque<common_msgs::cloudpants> m_cloudpantsMsgDeque_;

	long m_gpstime; //gps时间
	long m_curLidarStamp;
};
#endif
#ifdef ROS2_FOUND
#include <rclcpp/rclcpp.hpp>
#include <visualization_msgs/msg/marker.hpp>
#include <visualization_msgs/msg/marker_array.hpp>
#include <ament_index_cpp/get_package_share_directory.hpp>
#include "common_msgs_humble/msg/sensorobject.hpp"
#include "common_msgs_humble/msg/sensorobjects.hpp"
#include "common_msgs_humble/msg/sensorgps.hpp"
#include "common_msgs_humble/msg/cloudpant.hpp"
#include "common_msgs_humble/msg/cloudpants.hpp"
#include "common_msgs_humble/msg/elapsedtime.hpp"

#include "../../../../src/perception/commonlibrary/src/common.h"
#include "../../../../src/perception/commonlibrary/src/Hungarian.h"
#include "../../../../src/perception/commonlibrary/src/getiou.h"
#include "../../../../src/perception/commonlibrary/src/computeAssignmentMatrix.h"
#include "../../../../src/perception/commonlibrary/src/coordinateTransformation/wgs84_utm.h"
#include "../../../../src/perception/commonlibrary/src/coordinateTransformation/sensorAxisTransformation/sensorAxisTransformation.h" //从FusionAndTrk/src目录开始算再去找头文件路径
#include "../../../../src/perception/commonlibrary/src/visualize/sensorobjectsviewer.h"
#include "../../../../src/perception/commonlibrary/src/visualize/viewer.h"
#include "../../../../src/perception/commonlibrary/src/configManager/configManager.h"
#include "../../../../src/perception/commonlibrary/src/sensorobjects/cameraobjects.h"
#include "../../../../src/perception/commonlibrary/src/logger/logger.h"
#include "../../../../src/perception/commonlibrary/src/fileWriter/fileWriter.h"


using namespace std;
typedef long int64;

const static float   ANGLE_FACTOR = 0.017453; //m_pi/180.0

class CarFusion {
public:
	CarFusion(rclcpp::Node::SharedPtr nh);
	~CarFusion();

	rclcpp::Node::SharedPtr m_nh;
	rclcpp::QoS m_qos_profile{rclcpp::KeepLast(10)};  // 默认深度为10
	void run();
	void fusion();
	void objectFusion();
	void publishFusionObject();
	////// 订阅话题
	rclcpp::Subscription<common_msgs_humble::msg::Sensorobjects>::SharedPtr sub_apolloObjects;
	rclcpp::Subscription<common_msgs_humble::msg::Sensorgps>::SharedPtr sub_gps;
	rclcpp::Subscription<common_msgs_humble::msg::Cloudpants>::SharedPtr sub_cloudobjects;

	////// 发布话题
	rclcpp::Publisher<common_msgs_humble::msg::Sensorobjects>::SharedPtr pub_fusionObject;

	rclcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr pub_lidarObjectBBX;
	rclcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr pub_fusionObjectBBX;
	rclcpp::Publisher<common_msgs_humble::msg::Elapsedtime>::SharedPtr pub_fusiontrackingElapsedtime;
	rclcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr pub_cloudObjectsBBX;

	////// 回调函数
	void SubCallback_apolloObjects(const common_msgs_humble::msg::Sensorobjects::SharedPtr &msg);
	void SubCallback_gps(const common_msgs_humble::msg::Sensorgps::SharedPtr &msg);
	void SubCallback_cloudpants(const common_msgs_humble::msg::Cloudpants::SharedPtr &msg);
	void sensorTimeSynchro();
	
	//////功能函数
	template <typename T>
	void timeSynchro(std::deque<T>& msgDeque,const int64& curObjectFrameStamp, const int synchroFlag);
	void LatLonToLocalXY(double lon_car, double lat_car, float& x, float& y);


	void sensorobjectsBoxShow(const common_msgs_humble::msg::Sensorobjects &msg_source, const int& sensorType);

	void cloudpantsPreprocess(const common_msgs_humble::msg::Cloudpants& cloudpants, common_msgs_humble::msg::Sensorobjects& cloudObjects);
	void apolloObjectsPreprocess();
	int transCloudObjectType2CameraDetectionType(const int& cloudObjectType);
	
private:
	Common m_common;
	wgs84_utm m_wgs84Utm;
	SensorAxisTransformation m_sensorAxisTransformer;
	boost::shared_ptr<ConfigManager::ConfigManager> m_pConfigManager;
	std::shared_ptr<Logger> m_pcLogger;
	std::shared_ptr<spdlog::logger> m_pLogger;
	boost::shared_ptr<FileWriter> m_pTimeUseFileWriter;
	
	
	//外参
	float m_lidar2carBackRFU_X;
	float m_lidar2carBackRFU_Y;
	float m_lidar2carBackRFU_Z;
	
	float m_lidarFLU2carBackRFU_rollDegree;
	float m_lidarFLU2carBackRFU_pitchDegree;
	float m_lidarFLU2carBackRFU_yawDegree;
	
	float m_lidarRFU2carBackRFU_rollDegree;
	float m_lidarRFU2carBackRFU_pitchDegree;
	float m_lidarRFU2carBackRFU_yawDegree;
	
	Eigen::Vector3d m_lidarRFU2carBackRFU_eulerXYZDegree;
	Eigen::Vector3d m_lidarFLU2carBackRFU_eulerXYZDegree;
	Eigen::Vector3d m_lidar2carBackRFU_translation;

	
	//// 当前帧数据
	common_msgs_humble::msg::Sensorobjects m_lidarMsg;      //lidar data
	common_msgs_humble::msg::Sensorgps     m_gpsMsg ;       // gps data
	common_msgs_humble::msg::Cloudpants m_cloudpantsMsg; //cloudpants data
	common_msgs_humble::msg::Sensorobjects m_cloudObjectMsg; //cloudpants data
	common_msgs_humble::msg::Sensorobjects m_fusionObjectMsg; //cloudpants data
	
	std::vector<double> m_selfCarSpeed;
	std::vector<double> m_selfCarUTMPosition; //三维
	
	//锁变量
	std::mutex m_lidarMutex;
	std::mutex m_gpsMutex;
	std::mutex m_cloudpantsMutex;
	
	////数据容器
	std::deque<common_msgs_humble::msg::Sensorobjects> m_lidarMsgDeque_;
	std::deque<common_msgs_humble::msg::Sensorgps> m_gpsMsgDeque_;
	std::deque<common_msgs_humble::msg::Cloudpants> m_cloudpantsMsgDeque_;

	long m_gpstime; //gps时间
	long m_curLidarStamp;
};
#endif

#endif //SRC_FUSIONTRACKING_H
