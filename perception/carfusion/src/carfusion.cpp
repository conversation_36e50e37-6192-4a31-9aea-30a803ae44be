/********************************************************************************
* @author: shua<PERSON><PERSON><PERSON> han
* @date: 2023/6/21 下午5:48
* @version: 1.0
* @description: 
********************************************************************************/


#include "carfusion.h"

using namespace std;

#ifdef ROS1_FOUND

/***
* 1.订阅相关话题，做数据缓存（radar分两个容器缓存，100米外用于障碍物检测，100米内用于障碍物融合）
 *2.进行坐标转换（独立类）
 *3.多传感器目标融合
 * 4.目标跟踪
 * */

CarFusion::CarFusion(ros::NodeHandle nodeHandle){
	m_nh = nodeHandle;

	m_gpstime = 0;
	m_curLidarStamp = 0;
	
	m_lidar2carBackRFU_X = 0;
	m_lidar2carBackRFU_Y = 0;
	m_lidar2carBackRFU_Z = 0;
	
	m_lidarFLU2carBackRFU_rollDegree = 0;
	m_lidarFLU2carBackRFU_pitchDegree = 0;
	m_lidarFLU2carBackRFU_yawDegree = 0;
	
	m_lidarRFU2carBackRFU_rollDegree = 0;
	m_lidarRFU2carBackRFU_pitchDegree = 0;
	m_lidarRFU2carBackRFU_yawDegree = 0;
	
	m_lidarRFU2carBackRFU_eulerXYZDegree = Eigen::Vector3d::Zero();
	m_lidarFLU2carBackRFU_eulerXYZDegree = Eigen::Vector3d::Zero();
	m_lidar2carBackRFU_translation = Eigen::Vector3d::Zero();
	
	memset(&m_lidarMsg, 0, sizeof(m_lidarMsg));
	memset(&m_fusionObjectMsg, 0, sizeof(m_fusionObjectMsg));
	
	m_lidarMsg.obs.clear();
	m_fusionObjectMsg.obs.clear();

	m_selfCarSpeed.reserve(3);
	
	std::string nodeName = ros::this_node::getName(); //获取节点名称
	int nameIndex = nodeName.find_first_not_of("/");
	std::string nodeNameNoChar = nodeName.substr(nameIndex); //得到"sensrorradar"
	std::string nodePath = ros::package::getPath(nodeNameNoChar);
	std::string packagePath = nodePath + "/launch/params.yaml";
	m_pConfigManager = boost::make_shared<ConfigManager::ConfigManager>(packagePath);

	m_pcLogger = std::make_shared<Logger>(m_pConfigManager);
	m_pLogger = m_pcLogger->getLogger();

	m_pConfigManager->printParams(m_pLogger);

	// 检查日志记录器是否为异步类型
    if (typeid(*m_pLogger) == typeid(spdlog::async_logger)) {
        m_pLogger->info("Asynchronous logging is enabled.");
    } else {
        m_pLogger->info("Asynchronous logging is not enabled.");
    }

	m_pLogger->info("nodeName: {}", nodeName);
	m_pLogger->info("nodePath: {}", nodePath);
	m_pLogger->info("packagePath: {}", packagePath);

	m_nh.param(m_pConfigManager->m_carName + "/lidar2carBackRFU_X",m_lidar2carBackRFU_X,m_lidar2carBackRFU_X); //
	m_nh.param(m_pConfigManager->m_carName + "/lidar2carBackRFU_Y",m_lidar2carBackRFU_Y,m_lidar2carBackRFU_Y); //
	m_nh.param(m_pConfigManager->m_carName + "/lidar2carBackRFU_Z",m_lidar2carBackRFU_Z,m_lidar2carBackRFU_Z); //
	m_nh.param(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_rollDegree",m_lidarFLU2carBackRFU_rollDegree,m_lidarFLU2carBackRFU_rollDegree); //
	m_nh.param(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_pitchDegree",m_lidarFLU2carBackRFU_pitchDegree,m_lidarFLU2carBackRFU_pitchDegree); //
	m_nh.param(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_yawDegree",m_lidarFLU2carBackRFU_yawDegree,m_lidarFLU2carBackRFU_yawDegree); //
	m_nh.param(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_rollDegree",m_lidarRFU2carBackRFU_rollDegree,m_lidarRFU2carBackRFU_rollDegree); //
	m_nh.param(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_pitchDegree",m_lidarRFU2carBackRFU_pitchDegree,m_lidarRFU2carBackRFU_pitchDegree); //
	m_nh.param(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_yawDegree",m_lidarRFU2carBackRFU_yawDegree,m_lidarRFU2carBackRFU_yawDegree); //
	

	m_pLogger->info("carName: {}", m_pConfigManager->m_carName);
	m_pLogger->info("Copyright©2021-2023 VANJEE Technology. All rights reserved");

	
	m_lidarRFU2carBackRFU_eulerXYZDegree << m_lidarRFU2carBackRFU_rollDegree, m_lidarRFU2carBackRFU_pitchDegree, m_lidarRFU2carBackRFU_yawDegree;
	m_lidarFLU2carBackRFU_eulerXYZDegree << m_lidarFLU2carBackRFU_rollDegree, m_lidarFLU2carBackRFU_pitchDegree, m_lidarFLU2carBackRFU_yawDegree;
	m_lidar2carBackRFU_translation << m_lidar2carBackRFU_X, m_lidar2carBackRFU_Y, m_lidar2carBackRFU_Z;
	

	if(m_pConfigManager->m_isSaveTimeUseFile){
		m_pTimeUseFileWriter = boost::make_shared<FileWriter>(m_pConfigManager);
		std::string timestampStr = std::to_string(long(ros::Time::now().toSec() * 1000)) + ".csv";
		m_pTimeUseFileWriter->createFile(timestampStr);
		std::string dataHeader = "frameCount,trackedObjectSize,trackingTimeUse,allLidarOBUObjectSize,allLidarOBUTimeUse\n";
		m_pTimeUseFileWriter->writeHeader(dataHeader);
	}
	

	//订阅话题
	sub_apolloObjects = m_nh.subscribe("/apollo/tracked_objects",10,&CarFusion::SubCallback_apolloObjects,this);
	sub_gps = m_nh.subscribe("sensorgps", 200, &CarFusion::SubCallback_gps,this);
	sub_cloudobjects = m_nh.subscribe("/cloud/cloudobjects", 10, &CarFusion::SubCallback_cloudpants,this);
	
	//发布话题
	pub_lidarObjectBBX = m_nh.advertise<visualization_msgs::MarkerArray>(nodeName + "/lidarObjectBBX",100);
	pub_cloudObjectsBBX = m_nh.advertise<visualization_msgs::MarkerArray>(nodeName + "/cloudObjectsBBX", 100);
	pub_fusionObjectBBX = m_nh.advertise<visualization_msgs::MarkerArray>(nodeName + "/fusionObjectBBX",100);
	pub_fusionObject = m_nh.advertise<common_msgs::sensorobjects>("/objectTrack/track_results8CornerForRviz",10);
	pub_fusiontrackingElapsedtime = m_nh.advertise<common_msgs::elapsedtime>(nodeName + "/elapsedtime",100);
}

CarFusion::~CarFusion(){
	m_pConfigManager = nullptr;
	m_pcLogger = nullptr;
	m_pLogger = nullptr;
};

void CarFusion::run() {
	ros::Rate rate(30);
	
	while(ros::ok()) {
		ros::spinOnce();
		if(!m_pConfigManager->m_isUseRosBag){
			fusion();
		}
		rate.sleep();
	}

}

void CarFusion::fusion() {
	if(m_pConfigManager->m_isSimulateMode)
		return;

	//// 以lidar为主传感器,必须以lidar数据开始，如果第一帧是radar或者其他传感器退出
	m_lidarMsg.obs.clear();
	std::unique_lock<std::mutex> lidarLock(m_lidarMutex);
	if(!m_lidarMsgDeque_.empty()) {
		// std::lock_guard<std::mutex> lidarLock(m_lidarMutex);
		m_lidarMsg = m_lidarMsgDeque_.front();
		m_lidarMsgDeque_.pop_front();
		m_pLogger->info("m_lidarMsgDeque_ size: {}", m_lidarMsgDeque_.size());
	}
	else{
		m_pLogger->warn("m_lidarMsgDeque_ is empty");
		return;
	}
	lidarLock.unlock();

	static int lidarFrameCount = 0;
	m_pLogger->info("\n\n第 {} 帧lidar融合跟踪过程...................................", lidarFrameCount++);

	long curStartStamp = ros::Time::now().toSec() * 1000;
	float timeGap = curStartStamp - m_lidarMsg.timestamp;
	m_pLogger->info("curStartROSTimestamp = {:.3f}, m_lidarMsg timestamp = {:.3f}, timeGap(ms) = {:.3f}", curStartStamp / 1000.0, m_lidarMsg.timestamp / 1000.0, timeGap);
	if(!m_lidarMsgDeque_.empty()){
		m_pLogger->info("m_lidarMsgDeque_.front() timestamp = {:.3f}", m_lidarMsgDeque_.front().timestamp / 1000.0);
	}
	else{
		m_pLogger->info("m_lidarMsgDeque_.empty()");
	}
	m_curLidarStamp = m_lidarMsg.timestamp;

	long getLidarEndTime =  ros::Time::now().toSec()*1000;
	double getLidarTimeUse = (double)(getLidarEndTime - curStartStamp);
	m_pLogger->info("[getLidarTimeUse use time(ms)] = {}", getLidarTimeUse);

	//// lidar-GPS-radar-cloudObject时间同步
	sensorTimeSynchro();
	m_selfCarUTMPosition.clear();
	
	m_selfCarUTMPosition = m_wgs84Utm.getUTMPosition(m_gpsMsg.lon, m_gpsMsg.lat, m_gpsMsg.alt);
	//设置m_sensorAxisTransformer公共数据
	Eigen::Vector3d selfCarEulerDegrees{m_gpsMsg.pitch, m_gpsMsg.roll, m_gpsMsg.heading};
	m_sensorAxisTransformer.setSelfCarEulerDegrees(selfCarEulerDegrees);
	m_sensorAxisTransformer.setSelfCarUTMPosition(m_selfCarUTMPosition);

	m_selfCarSpeed.clear();
	//第一辆车发出NED,看做是ENU顺序,三轴角度和速度都看作是ENU
	m_selfCarSpeed = m_sensorAxisTransformer.ENU2CarBackRFUAxis(Eigen::Vector3d{m_gpsMsg.pitch, m_gpsMsg.roll, m_gpsMsg.heading},
	                                                            Eigen::Vector3d{m_gpsMsg.speedE, m_gpsMsg.speedN, -m_gpsMsg.speedD});


	apolloObjectsPreprocess();
	sensorobjectsBoxShow(m_lidarMsg, COMMON::SensorType::LIDAR);
	

	// cloudObjects预处理
	cloudpantsPreprocess(m_cloudpantsMsg, m_cloudObjectMsg);
	sensorobjectsBoxShow(m_cloudObjectMsg, COMMON::SensorType::CLOUDPLATFORM);
	
	
	////目标融合
	m_gpstime = m_lidarMsg.timestamp;

	
	long preprocessEndTime =  ros::Time::now().toSec()*1000;
	m_pLogger->info("开始融合.......");
	objectFusion();
	sensorobjectsBoxShow(m_lidarMsg, COMMON::SensorType::FUSION);
	long fusionEndTime =  ros::Time::now().toSec()*1000;
	double fusionTimeUse = (double)(fusionEndTime - preprocessEndTime);
	m_pLogger->info("[fusion use time(ms)] = {}", fusionTimeUse);

	publishFusionObject();
	
	common_msgs::elapsedtime fusiontrackingElapsedtimeMsg;
	fusiontrackingElapsedtimeMsg.time = fusiontrackingTimeUse;
	pub_fusiontrackingElapsedtime.publish(fusiontrackingElapsedtimeMsg);
	
	static double maxFusionTrackingTimeUse = 0;
	if(fusiontrackingTimeUse > maxFusionTrackingTimeUse){
		maxFusionTrackingTimeUse = fusiontrackingTimeUse;
		m_pLogger->warn("get max time: : maxFusionTrackingTimeUse(ms) = {}", maxFusionTrackingTimeUse);
	}
	m_pLogger->info("max carfusion use time(ms) = {}", maxFusionTrackingTimeUse);
}
/***
 * 订阅lidar检测话题
 * @param msg lidar检测目标
 */
void CarFusion::SubCallback_apolloObjects(const common_msgs::sensorobjects::ConstPtr &msg){
	static double preStamp = -1;
	// m_pLogger->info("Entering callback, preStamp = {:.3f}, current msg timestamp = {:.3f}", preStamp, msg->timestamp / 1000.0);
	if(preStamp < 0){
		preStamp = msg->timestamp;
		m_pLogger->info("callback first msg timestamp = {:.3f}", msg->timestamp / 1000.0);
	}
	else{
		float timeGap = msg->timestamp - preStamp;
		if(timeGap >= 200){
			m_pLogger->warn("检测丢帧: callback cur timestamp = {:.3f}, pre timestamp = {:.3f}, timeGap(ms) = {:.3f}",msg->timestamp / 1000.0, preStamp / 1000.0, timeGap);
		}
		preStamp = msg->timestamp;
	}
	if(m_pConfigManager->m_isUseRosBag){
		double curtimestamp = ros::Time::now().toSec() * 1000.0;
		float timeGap = curtimestamp - msg->timestamp;
		m_pLogger->info("sensorlidar2 callback current ros time = {:.3f}, msg timestamp = {:.3f}, timeGap(ms) = {:.3f}",curtimestamp/ 1000.0, msg->timestamp / 1000.0, timeGap);
		std::unique_lock<std::mutex> lidarLock(m_lidarMutex);
		m_lidarMsgDeque_.emplace_back(*msg);
		
		m_lidarMsg = *msg;
		apolloObjectsPreprocess();
		m_pLogger->info("apolloObjectsPreprocess");
		objectFusion();
		m_pLogger->info("objectFusion");
		sensorobjectsBoxShow(m_fusionObjectMsg, COMMON::SensorType::FUSION);
		m_pLogger->info("sensorobjectsBoxShow");
		publishFusionObject();
		m_pLogger->info("publishFusionObject");
		lidarLock.unlock();
	}
	else{
		double curtimestamp = ros::Time::now().toSec() * 1000.0;
		float timeGap = curtimestamp - msg->timestamp;
		m_pLogger->info("sensorlidar2 callback current ros time = {:.3f}, msg timestamp = {:.3f}, timeGap(ms) = {:.3f}",curtimestamp / 1000.0, msg->timestamp / 1000.0, timeGap);
		// std::lock_guard<std::mutex> lidarLock(m_lidarMutex);
		std::unique_lock<std::mutex> lidarLock(m_lidarMutex);
		m_lidarMsgDeque_.emplace_back(*msg);
		lidarLock.unlock();
	}
}


void CarFusion::SubCallback_cloudpants(const common_msgs::cloudpants::ConstPtr &msg){
	std::unique_lock<std::mutex> cloudpantsLock(m_cloudpantsMutex);
	if(!m_pConfigManager->m_isSimulateMode){// 实车测试
		m_cloudpantsMsgDeque_.emplace_back(*msg);
		if(m_cloudpantsMsgDeque_.size() > 100)
			m_cloudpantsMsgDeque_.pop_front();
	}
	else{// 仿真模式
		// cloudObjectPants 时间同步
		{
		  m_pLogger->info("virtualObject-gps time sync: ");
		  std::unique_lock<std::mutex> gpsLock(m_gpsMutex);
		  timeSynchro(m_gpsMsgDeque_, (*msg).timestamp, COMMON::SensorType::GPS);    //20221027 lidar检测与GPS信息的时间同步
		  if(!m_gpsMsgDeque_.empty()){
			  m_gpsMsg = m_gpsMsgDeque_.front();//20221027 lidar检测与GPS信息的时间同步
		  }
		  else{
			  m_pLogger->warn("WARNING: gps msg is empty, please check! ");
		  }
		  gpsLock.unlock();
		}
		m_selfCarSpeed.clear();
		m_pLogger->info("Car heading(deg) = {}", m_gpsMsg.heading);
		//第一辆车发出NED,看做是ENU顺序,三轴角度和速度都看作是ENU
		m_selfCarSpeed = m_sensorAxisTransformer.ENU2CarBackRFUAxis(Eigen::Vector3d{m_gpsMsg.pitch, m_gpsMsg.roll, m_gpsMsg.heading},
																	Eigen::Vector3d{m_gpsMsg.speedE, m_gpsMsg.speedN, -m_gpsMsg.speedD});

		m_gpstime = m_gpsMsg.timestamp;//ros::Time::now().toSec() * 1000;
		m_pLogger->info("use virtual object");
		// cloudObjects预处理
		cloudpantsPreprocess(*msg, m_cloudObjectMsg);
		sensorobjectsBoxShow(m_cloudObjectMsg, COMMON::SensorType::CLOUDPLATFORM);

		publishFusionObject();
	}
	cloudpantsLock.unlock();
}

/***
 * 订阅gps话题
 * @param msg gps话题
 */
void CarFusion::SubCallback_gps(const common_msgs::sensorgps::ConstPtr &msg){
	// std::lock_guard<std::mutex> gpsLock(m_gpsMutex);
	std::unique_lock<std::mutex> gpsLock(m_gpsMutex);
	m_gpsMsgDeque_.emplace_back(*msg);
	if(m_gpsMsgDeque_.size() > 100)
		m_gpsMsgDeque_.pop_front();
	gpsLock.unlock();
}


void CarFusion::sensorTimeSynchro(){

	{
		m_pLogger->info("lidar-gps time sync:");
		// std::lock_guard<std::mutex> gpsLock(m_gpsMutex);
		std::unique_lock<std::mutex> gpsLock(m_gpsMutex);
		timeSynchro(m_gpsMsgDeque_, m_lidarMsg.timestamp, COMMON::SensorType::GPS);    //20221027 lidar检测与GPS信息的时间同步
		if(!m_gpsMsgDeque_.empty()){
			m_gpsMsg = m_gpsMsgDeque_.front();//20221027 lidar检测与GPS信息的时间同步
		}
		else{
			m_pLogger->warn("m_gpsMsgDeque_ is empty.");
		}
		gpsLock.unlock();
	}


	{
		m_pLogger->info("lidar-cloudpants time sync:");
		// std::lock_guard<std::mutex> cloudpantsLock(m_cloudpantsMutex);
		std::unique_lock<std::mutex> cloudpantsLock(m_cloudpantsMutex);
		m_cloudpantsMsg.pants.clear();
		timeSynchro(m_cloudpantsMsgDeque_, m_lidarMsg.timestamp, COMMON::SensorType::CLOUDPLATFORM);
		if(!m_cloudpantsMsgDeque_.empty()){
			m_cloudpantsMsg = m_cloudpantsMsgDeque_.front();
			m_cloudpantsMsgDeque_.pop_front();
			m_pLogger->info("cloudpants freameID = {}", m_cloudpantsMsg.frameId);
		}
		else{
			m_pLogger->warn("m_cloudpantsMsgDeque_ is empty.");
		}
		cloudpantsLock.unlock();
	}
}


/***
 * 20221027 lidar检测与传感器信息的时间同步 20220224 添加同步话题标志位
 * @tparam T 数据类型
 * @param msgDeque 需要同步的传感器（非主传感器）
 * @param curObjectFrameStamp 当前lidar检测时间
 * @param synchroFlag 同步标志位，1：lidar-gps，2：lidar-radar 3:lidar-obu
 */
template <typename T>
void CarFusion::timeSynchro(std::deque<T>& msgDeque,const int64& curObjectFrameStamp, const int synchroFlag){
	std::mutex dequeMutex;
	std::unique_lock<std::mutex> dequeLock(dequeMutex);

	if(msgDeque.empty()) {
		m_pLogger->warn("msgDeque empty,no need to syncchro.");
		return;
	}
	double curFrameStamp = (double)curObjectFrameStamp / 1000.0;

	//找到容器中数据时间戳小于同步时间的最近索引
	int curMsgIndex = 0;
	int msgDequeSize = msgDeque.size();
	for (int i = 0; i < msgDequeSize; ++i) {
		T curMsg = msgDeque[i];
		if(curMsg.timestamp / 1000.0  > curFrameStamp )
			break;
		curMsgIndex = i;
	}
	
	
	if(curMsgIndex + 1 == msgDeque.size()){//容器中数据时间戳都小于同步时间,只保留最后一个数据
		double lastMsgAndLidarDectionTimeGap = abs(msgDeque[curMsgIndex].timestamp / 1000.0 - curFrameStamp );
		if(curMsgIndex > 0){//等于0的情况 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	else{
		if(curMsgIndex + 1 <= msgDeque.size()){
			double lastMsgAndLidarDectionTimeGap = abs(msgDeque[curMsgIndex].timestamp / 1000.0 - curFrameStamp );
			double curMsgAndLidarDectionTimeGap = abs(msgDeque[curMsgIndex + 1].timestamp / 1000.0 - curFrameStamp);
			if(abs(curFrameStamp - msgDeque[curMsgIndex].timestamp / 1000.0) >//lidar前一帧
			   abs(curFrameStamp - msgDeque[curMsgIndex + 1].timestamp / 1000.0)){//lidar后一帧
				curMsgIndex += 1;//取最近的一帧数据
			}
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	float timeGap = curFrameStamp * 1000 - msgDeque.front().timestamp;
	static float minTimeGap = FLT_MAX, maxTimeGap = FLT_MIN;
    if (abs(timeGap) < abs(minTimeGap)) {
        minTimeGap = abs(timeGap);
    }
    if (abs(timeGap) > abs(maxTimeGap)) {
        maxTimeGap = abs(timeGap);
    }
   
	std::string l_sensorName = "UNKNOWN";
	auto it  = COMMON::SensorTypeMap.find(synchroFlag);
	if(it != COMMON::SensorTypeMap.end())
		l_sensorName = it->second;
	dequeLock.unlock();
	m_pLogger->info("lidar-{} 时间差(ms): {:.3f}, 当前msg时间 = {:.3f}, 当前lidar检测时间 = {:.3f}", 
					l_sensorName, timeGap, msgDeque.front().timestamp / 1000.0, curFrameStamp);
	m_pLogger->info("Min time gap: {:.3f}, Max time gap: {:.3f}", minTimeGap, maxTimeGap);
}


/***
 * 可视化目标msg，用于调试
 * @param msg_source 目标msg：carFrontRFU2CarFrontFLU
 */
void CarFusion::sensorobjectsBoxShow(const common_msgs::sensorobjects &msg_source, const int& sensorType){
	std::string frameIDInfo = "car";

	visualization_msgs::MarkerArray objectsMarkerArray;

	visualization_msgs::Marker marker;
	marker.action=visualization_msgs::Marker::DELETEALL;
	objectsMarkerArray.markers.emplace_back(marker);

	int msgSourceObsSize = msg_source.obs.size();
	int classColorSize = class_color.size();
	int speedSourceValueSize = v_speedSourceValue.size();
	vector<float> color{0.5, 0, 0};
	geometry_msgs::Point p0, p1, p2, p3;
	geometry_msgs::Point p4, p5, p6, p7;
	#pragma omp parallel for
	for (int i=0; i<msgSourceObsSize; i++){
		const auto& obs = msg_source.obs[i];
		int classification = int(obs.classification);

		// 距离目标不显示框
		if(classification == COMMON::LidarDetectionClassification::Unknown && m_pConfigManager->m_isUseRosBag == false)
			continue;

		if(classification < classColorSize){
			color = class_color[classification];
		}

		visualization_msgs::Marker line_list_detect;
		double stamp = msg_source.timestamp / 1000.0;
		line_list_detect.header.frame_id  = frameIDInfo;
		//line_list_detect.header.stamp = ros::Time::now();
		line_list_detect.header.stamp = ros::Time().fromSec(stamp);//TODO  lidar时间戳 //ros::Time::now()
		line_list_detect.ns = "points_and_lines";
		line_list_detect.lifetime =ros::Duration();//0.1
		line_list_detect.action = visualization_msgs::Marker::ADD;
		line_list_detect.pose.orientation.w = 1.0;
		line_list_detect.id = i;
		line_list_detect.type = visualization_msgs::Marker::LINE_LIST;
		//line width
		line_list_detect.scale.x = 0.1;
		//line_list_detect.scale.y = 0.1;
		//line_list_detect.scale.z = 0.1;
		//color green
		line_list_detect.color.a = 1;//透明度
		if(sensorType == COMMON::SensorType::LIDAR){
			line_list_detect.color.a = 1;//透明度
			line_list_detect.color.r = color[0]; //检测颜色
			line_list_detect.color.g = color[1];
			line_list_detect.color.b = color[2];
		}
		else if(sensorType == COMMON::SensorType::RADAR){
			line_list_detect.color.r = 0; //检测颜色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::OBU){
			line_list_detect.color.r = 0; //蓝色
			line_list_detect.color.g = 0;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::CLOUDPLATFORM){
			line_list_detect.color.r = 0; //检测颜色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 0;
		}
		else if(sensorType == COMMON::SensorType::FUSION){
			line_list_detect.color.a = 1;//透明度
			line_list_detect.color.r = 0; //检测颜色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 0;
		}
		else if(sensorType == COMMON::SensorType::FUSIONAllOBJECTS){
			line_list_detect.color.r = 1; //洋红色
			line_list_detect.color.g = 0;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::TRACKING){
			line_list_detect.color.r = 1; //洋红色
			line_list_detect.color.g = 0.5;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::V2IFUSION){
			line_list_detect.color.r = 1; //洋红色
			line_list_detect.color.g = 0.5;
			line_list_detect.color.b = 1;
		}
		else{

		}


		int obj_class = int(msg_source.obs[i].classification);
		
		switch (sensorType) {
			case COMMON::SensorType::FUSION:
			{
				float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				vector<double> boxInfo = {obs.x,obs.y, obs.z,
										obs.length, obs.width, obs.height, headingAnticlockwise};
				vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				break;

				// const auto& points = obs.points;
				// p0.x = points[0].x;		p0.y = points[0].y;		p0.z = points[0].z;
				// p1.x = points[1].x;		p1.y = points[1].y;		p1.z = points[1].z;
				// p2.x = points[2].x;		p2.y = points[2].y;		p2.z = points[2].z;
				// p3.x = points[3].x;		p3.y = points[3].y;		p3.z = points[3].z;
				// p4.x = points[4].x;		p4.y = points[4].y;		p4.z = points[4].z;
				// p5.x = points[5].x;		p5.y = points[5].y;		p5.z = points[5].z;
				// p6.x = points[6].x;		p6.y = points[6].y;		p6.z = points[6].z;
				// p7.x = points[7].x;		p7.y = points[7].y;		p7.z = points[7].z;


				// p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y;		p0.z = msg_source.obs[i].points[0].z;
				// p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y;		p1.z = msg_source.obs[i].points[1].z;
				// p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y;		p2.z = msg_source.obs[i].points[2].z;
				// p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y;		p3.z = msg_source.obs[i].points[3].z;
				// p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y;		p4.z = msg_source.obs[i].points[4].z;
				// p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y;		p5.z = msg_source.obs[i].points[5].z;
				// p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y;		p6.z = msg_source.obs[i].points[6].z;
				// p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
			case COMMON::SensorType::TRACKING:
			{
				// float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				// vector<double> boxInfo = {obs.x,obs.y, obs.z,
				// 						obs.length, obs.width, obs.height, headingAnticlockwise};
				// vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				// p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				// p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				// p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				// p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				// p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				// p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				// p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				// p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				// break;

				const auto& points = obs.points;
				p0.x = points[0].x;		p0.y = points[0].y;		p0.z = points[0].z;
				p1.x = points[1].x;		p1.y = points[1].y;		p1.z = points[1].z;
				p2.x = points[2].x;		p2.y = points[2].y;		p2.z = points[2].z;
				p3.x = points[3].x;		p3.y = points[3].y;		p3.z = points[3].z;
				p4.x = points[4].x;		p4.y = points[4].y;		p4.z = points[4].z;
				p5.x = points[5].x;		p5.y = points[5].y;		p5.z = points[5].z;
				p6.x = points[6].x;		p6.y = points[6].y;		p6.z = points[6].z;
				p7.x = points[7].x;		p7.y = points[7].y;		p7.z = points[7].z;

				// p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y;		p0.z = msg_source.obs[i].points[0].z;
				// p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y;		p1.z = msg_source.obs[i].points[1].z;
				// p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y;		p2.z = msg_source.obs[i].points[2].z;
				// p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y;		p3.z = msg_source.obs[i].points[3].z;
				// p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y;		p4.z = msg_source.obs[i].points[4].z;
				// p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y;		p5.z = msg_source.obs[i].points[5].z;
				// p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y;		p6.z = msg_source.obs[i].points[6].z;
				// p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
			case COMMON::SensorType::LIDAR:
			{
				float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				vector<double> boxInfo = {obs.x,obs.y, obs.z,
										obs.length, obs.width, obs.height, headingAnticlockwise};
				vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				break;
			}
			default:
			{
				// float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				// vector<double> boxInfo = {obs.x,obs.y, obs.z,
				// 						obs.length, obs.width, obs.height, headingAnticlockwise};
				// vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				// p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				// p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				// p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				// p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				// p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				// p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				// p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				// p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				// break;

				const auto& points = obs.points;
				p0.x = points[0].x;		p0.y = points[0].y;		p0.z = points[0].z;
				p1.x = points[1].x;		p1.y = points[1].y;		p1.z = points[1].z;
				p2.x = points[2].x;		p2.y = points[2].y;		p2.z = points[2].z;
				p3.x = points[3].x;		p3.y = points[3].y;		p3.z = points[3].z;
				p4.x = points[4].x;		p4.y = points[4].y;		p4.z = points[4].z;
				p5.x = points[5].x;		p5.y = points[5].y;		p5.z = points[5].z;
				p6.x = points[6].x;		p6.y = points[6].y;		p6.z = points[6].z;
				p7.x = points[7].x;		p7.y = points[7].y;		p7.z = points[7].z;
				break;
			}
		}

		//bottom
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p1);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p2);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p3);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p0);
		//top
		line_list_detect.points.push_back(p4); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p5); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p6); line_list_detect.points.push_back(p7);
		line_list_detect.points.push_back(p7); line_list_detect.points.push_back(p4);
		//side
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p4);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p7);
		//direction
		// if(sensorType == COMMON::SensorType::TRACKING){
		// 	// 方向箭头
		// 	geometry_msgs::Point headingArrow1, headingArrow2, headingArrow3, headingArrow4;
		// 	headingArrow1.x = (p4.x + p5.x)/2;
		// 	headingArrow1.y = (p4.y + p5.y)/2;
		// 	headingArrow1.z =  p4.z;
			
		// 	headingArrow2.x = (p4.x*3 + p7.x)/4;
        //     headingArrow2.y = (p4.y*3 + p7.y)/4;
        //     headingArrow2.z =p4.z;
        //     line_list_detect.points.push_back(headingArrow1);
        //     line_list_detect.points.push_back(headingArrow2);

		// 	headingArrow3.x = (p5.x*3 + p6.x)/4;
        //     headingArrow3.y = (p5.y*3 + p6.y)/4;
        //     headingArrow3.z =p6.z;
        //     line_list_detect.points.push_back(headingArrow1);
        //     line_list_detect.points.push_back(headingArrow3);

        //     headingArrow4.x = (p5.x + p4.x+ p7.x + p6.x)/4;
        //     headingArrow4.y = (p5.y + p4.y + p7.y + p6.y)/4;
        //     headingArrow4.z =p4.z;
        //     line_list_detect.points.push_back(headingArrow1);
        //     line_list_detect.points.push_back(headingArrow4);
		// }
		


		// radar不画框，其他画框
		if(sensorType != COMMON::SensorType::RADAR){
			objectsMarkerArray.markers.push_back(line_list_detect);
		}
		visualization_msgs::Marker text;
		text.header.frame_id = frameIDInfo;
		text.header.stamp = ros::Time().fromSec(stamp); //ros::Time::now()
		text.ns = "box";
		text.action = visualization_msgs::Marker::ADD;
		text.lifetime =ros::Duration();//0.1
		text.pose.orientation.w = 1;
		text.pose.position.x = msg_source.obs[i].x;
		text.pose.position.y = msg_source.obs[i].y;
		text.pose.position.z = msg_source.obs[i].z;
		text.id = i;
		text.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
		text.scale.z = 0.3;

		text.color.a = 1;//透明度
		text.color.r = 1;
		text.color.g = 1;
		text.color.b = 1;

		//添加目标横纵向速度信息
		std::string vxString = "", vyString = "";
		if(sensorType == COMMON::SensorType::RADAR){
			ostringstream str;
			str << std::setiosflags(std::ios::fixed) << std::setprecision(2) << msg_source.obs[i].relspeedx;
			vxString = str.str();
			str.str("");//清空数据
			str <<  msg_source.obs[i].relspeedy;
			vyString = str.str();
		}
		else{
			ostringstream str;
			str << std::setiosflags(std::ios::fixed) << std::setprecision(2) << msg_source.obs[i].relspeedx + m_selfCarSpeed[0];
			vxString = str.str();
			str.str("");//清空数据
			str <<  msg_source.obs[i].relspeedy + m_selfCarSpeed[1];
			vyString = str.str();
		}
		

		//添加跟踪类型
		//20221103 //0.初始目标 1.跟踪 2.lidar 3.lidar-radar-camera 4.融合中lidar-radar -20220908 20230111 简化
		int speedSourceValue = (int)msg_source.obs[i].value;
		std::string speedSource = speedSourceValue < speedSourceValueSize ? v_speedSourceValue[speedSourceValue] : v_speedSourceValue[speedSourceValueSize - 1];

		//添加目标运动状态
		//20220908 20230111 简化
		//std::string motionInfo = msg[i].motionInfo < v_motionInfo.size() ? v_motionInfo[msg[i].motionInfo] : v_motionInfo[v_motionInfo.size() - 1];
		std::string confidence = std::to_string(msg_source.obs[i].confidence);
		confidence = confidence.substr(0,4);
		string positionX = std::to_string(msg_source.obs[i].x);
		string positionY = std::to_string(msg_source.obs[i].y);
		// 显示：id
		// 第几次匹配成功-预测帧数
		// 类别-速度来源-运动信息
		// 横向速度-纵向速度
		// 置信度-驾驶意图-radarIndex-radarObjectID
		switch (sensorType) {
			case COMMON::SensorType::LIDAR:
				text.text =
						"Lidar:\nID:" +std::to_string(msg_source.obs[i].id)
						+ "\nX:" + positionX + "\nY:" + positionY
                        + "\nlabel:" + std::to_string(msg_source.obs[i].classification)
						+ "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                        + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180.0 / M_PI)
						+ "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::RADAR:
				text.text = "Radar:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::OBU:
				text.text = "OBU:\nID:" +std::to_string(msg_source.obs[i].id)
							+ "\nX:" + positionX + "\nY:" + positionY
							+ "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
							+ "\nlabel:" + std::to_string(msg_source.obs[i].classification)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
							+ "\nabsVx:" + vxString + "\nabsVy:" + vyString
							+ "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180.0 / M_PI)
							+ "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::CLOUDPLATFORM:
				text.text = "Cloudplatform:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::FUSION:
				text.text = "Fusion:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
							+ "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::FUSIONAllOBJECTS:
				text.text = "FUSIONAllOBJECTS:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::TRACKING:
				text.text = "Tracking:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
                            //+ "-drivingIntent:" + std::to_string(msg_source.obs[i].drivingIntent)
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			case COMMON::SensorType::V2IFUSION:
				text.text = "V2IFUSION:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
                            //+ "-drivingIntent:" + std::to_string(msg_source.obs[i].drivingIntent)
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarIndex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarObjectID);
				break;
			default:
				break;
		}
		objectsMarkerArray.markers.push_back(text);

		// ID 显示
		if(m_pConfigManager->m_isUseRosBag 
			&& (sensorType == COMMON::SensorType::TRACKING || sensorType == COMMON::SensorType::V2IFUSION)){
			visualization_msgs::Marker objectsInfoMarker;
			objectsInfoMarker.header.frame_id = frameIDInfo;
			objectsInfoMarker.header.stamp = ros::Time().fromSec(stamp);
			objectsInfoMarker.ns = "ID";
			objectsInfoMarker.id = i;
			objectsInfoMarker.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
			objectsInfoMarker.lifetime = ros::Duration();
			objectsInfoMarker.action = visualization_msgs::Marker::ADD;

			objectsInfoMarker.pose.orientation.w = 1;
			geometry_msgs::Point  p_temp;
			p_temp.x = (p4.x + p5.x)/2;
			p_temp.y = (p4.y + p5.y)/2;
			p_temp.z =  p4.z;
			objectsInfoMarker.pose.position.x = p_temp.x + 0.3*(p5.y-p4.y);
			objectsInfoMarker.pose.position.y = p_temp.y + 0.3*(p5.y-p4.y);
			objectsInfoMarker.pose.position.z = p_temp.z + 0.4*(p5.z-p4.z);

			objectsInfoMarker.scale.z = 2.4;
			objectsInfoMarker.color.a = 1;
			objectsInfoMarker.color.r = 1;
			objectsInfoMarker.color.g =1;
			objectsInfoMarker.color.b =0;
			objectsInfoMarker.text = std::to_string(msg_source.obs[i].id);
			objectsMarkerArray.markers.push_back(objectsInfoMarker);
		}
		
	}
	switch (sensorType) {
		case COMMON::SensorType::LIDAR:
			pub_lidarObjectBBX.publish(objectsMarkerArray);
			break;
		case COMMON::SensorType::RADAR:
			pub_radarObjectBBX.publish(objectsMarkerArray); //
			break;
		case COMMON::SensorType::CLOUDPLATFORM:
			pub_cloudObjectsBBX.publish(objectsMarkerArray);
			break;
		case COMMON::SensorType::FUSION:
			pub_fusionObjectBBX.publish(objectsMarkerArray); //
			break;
		case COMMON::SensorType::TRACKING:
			pub_trackingObjectBBX.publish(objectsMarkerArray);
			break;
		case COMMON::SensorType::V2IFUSION:
			pub_v2iFusionObjectBBX.publish(objectsMarkerArray);
			break;
		default:
			break;
	}
	objectsMarkerArray.markers.clear();

}




void CarFusion::LatLonToLocalXY(double lon_car, double lat_car, float& x, float& y)
{
	const double R_e = 6378137.0;       // 椭球体长半轴
    const double R_f = 6356752.314245;  // 椭球体短半轴
    const double e_1 = sqrt(pow(R_e, 2) - pow(R_f, 2)) / R_e;   // 第一偏心率
    

    double sin_lat1 = sin(m_pConfigManager->m_hdMapLatitude * M_PI / 180.0);     	// sin of start lat
    double cos_lat1 = cos(m_pConfigManager->m_hdMapLatitude * M_PI / 180.0); 	    // sec of start lat
    double square_e = e_1 * e_1;
    double square_sin_lat1 = sin_lat1 * sin_lat1;

    double R_n = R_e / (sqrt(1 - square_e * square_sin_lat1));   				// 卯酉面等效曲率半径 (lon1, lat1)
    double R_m = (R_n * (1.0 - square_e)) / (1.0 - square_e * square_sin_lat1);   	// 子午面等效曲率半径 (lon1, lat1)

    x = (lon_car - m_pConfigManager->m_hdMapLongitude) * M_PI / 180.0 * R_n * cos_lat1;
    y = (lat_car - m_pConfigManager->m_hdMapLatitude) * M_PI / 180.0 * R_m;

}

// 目标转换到图中
// 感知目标位置float x, float y, 车的图坐标 float x_car, float y_car, 航向float heading_car,   
// 输出坐标 float& x_obj, float& y_obj
void CarFusion::transformToAbsoluteCoordinates(float x, float y, float x_car, float y_car, float heading_car,
												 float& x_obj, float& y_obj){
    x_obj = x_car + x*std::cos(heading_car) + y*std::sin(heading_car);
    y_obj = y_car - x*std::sin(heading_car) + y*std::cos(heading_car);

}





void CarFusion::apolloObjectsPreprocess(){
	//lidar目标处理
	int lidarObjectsSize = m_lidarMsg.obs.size();
	m_pLogger->info("lidar obs raw size = {}", lidarObjectsSize);
	vector<double> boxInfo(7, 0);
	vector<vector<double>> pointsVector(8, vector<double>(3, 0.0));
	std::vector<double> objectSpeedInCarBackFRU(3);
	vector<double> objectUTM(3, 0);
	for(int i = 0; i < lidarObjectsSize; i++){
		common_msgs::Sensorobject& lidarObject = m_lidarMsg.obs[i];
		objectUTM = {lidarObject.x, lidarObject.y,lidarObject.z};
		m_pLogger->info("objectUTM: {:.7f}, lat: {:.7f}",  lidarObject.x, lidarObject.y);
		WGS84Corr lla;//填充经纬度
		m_wgs84Utm.UTMXYToLatLon(objectUTM[0], objectUTM[1],
								 m_pConfigManager->m_cityUTMCode, false, lla);

		Eigen::Vector3d inputUTMPosition{objectUTM[0] - m_selfCarUTMPosition[0],
										 objectUTM[1] - m_selfCarUTMPosition[1],
		                                  objectUTM[2]};
		std::vector<double> objectPositionInCarBackFRU = m_sensorAxisTransformer.ENU2BodyAxis(inputUTMPosition);
		

		// TODO 确认apollo的跟踪目标航向角，先按正北顺时针0~2pi计算
		float objectAngleDegreeInCarBackRFU_Clockwise = m_sensorAxisTransformer.NorthClockwise2CarBackRFU(lidarObject.azimuth * 180 / M_PI, m_gpsMsg.heading);
		float objectAngleRadInCarBackRFU_Clockwise = objectAngleDegreeInCarBackRFU_Clockwise * M_PI / 180.0;
		

		lidarObject.x = objectPositionInCarBackFRU[0];
		lidarObject.y = objectPositionInCarBackFRU[1];
		lidarObject.z = objectPositionInCarBackFRU[2];
		m_pLogger->info("lidarObject: {:.7f}, lat: {:.7f}",  lidarObject.x, lidarObject.y);
		lidarObject.longtitude = lla.log / M_PI * 180;
		lidarObject.latitude = lla.lat / M_PI * 180;
		lidarObject.altitude = 0;
		Eigen::Vector3d UTMRelativeSpeed{lidarObject.relspeedx, lidarObject.relspeedy, 0};
		objectSpeedInCarBackFRU = m_sensorAxisTransformer.ENU2BodyAxis(UTMRelativeSpeed);
		
		lidarObject.relspeedx = objectSpeedInCarBackFRU[0];//UTM速度转到ENU速度
		lidarObject.relspeedy = objectSpeedInCarBackFRU[1];
		lidarObject.azimuth = m_common.normalizeAngle(objectAngleRadInCarBackRFU_Clockwise * M_PI / 180.0); 

		lidarObject.value = COMMON::SensorType::LIDAR;
		lidarObject.radarindex = UINT8_MAX; // radarindex 未融合的目标设置索引为255
		lidarObject.radarobjectid = UINT8_MAX;
		//计算8角点
		float headingClockwise = 2.0 * M_PI - objectAngleRadInCarBackRFU_Clockwise; // 显示 -- 跟画框有关:画框需要逆时针，保存CSV需要顺时针，发送需要顺时针
		boxInfo = {lidarObject.x, lidarObject.y, lidarObject.z,
				  lidarObject.length, lidarObject.width, lidarObject.height,
		           headingClockwise};
		pointsVector = m_common.boxes_to_corners_3d(boxInfo);
		lidarObject.points.clear();
	
		for(const auto& pointVector:pointsVector){
			common_msgs::Point3d  cornerPoint;
			cornerPoint.x = pointVector[0];
			cornerPoint.y = pointVector[1];
			cornerPoint.z = pointVector[2];
			lidarObject.points.emplace_back(std::move(cornerPoint));
		}
		
	}
}

void CarFusion::cloudpantsPreprocess(const common_msgs::cloudpants& cloudpants, common_msgs::sensorobjects& cloudObjects){
	cloudObjects.obs.clear();
	cloudObjects.timestamp = cloudpants.timestamp;
	cloudObjects.gpstime = cloudpants.timestamp;
	cloudObjects.isvalid = 1;
	
	if(cloudpants.pants.empty()){
		m_pLogger->warn("cloudObjects is empty");
		return;
	}
	
	
	tagUTMCorr selfCarUTM;
	m_wgs84Utm.LatLonToUTMXY(m_gpsMsg.lat / 180.0 * M_PI, m_gpsMsg.lon / 180.0 * M_PI, selfCarUTM);
	Eigen::Vector3d selfCarEulerXYZDegree{m_gpsMsg.roll, m_gpsMsg.pitch, m_gpsMsg.heading};
	m_pLogger->info("m_gpsMsg.heading = {:.3f}", m_gpsMsg.heading);
	
	Eigen::Vector3d selfCarUTMAsTranslation{selfCarUTM.x, selfCarUTM.y, 0};
	m_pLogger->info("cloudpants object size = {}", cloudpants.pants.size());
	
	for (const auto& cloudpant:cloudpants.pants) {
		common_msgs::sensorobject cloudObject;
		//	1. 经纬度转UTM
		tagUTMCorr utm;
		m_wgs84Utm.LatLonToUTMXY(cloudpant.latitude / 180.0 * M_PI, cloudpant.longitude / 180.0 * M_PI, utm);
		Eigen::Vector3d objectPositionUTM{utm.x, utm.y, 0};
		
		//2.UTM转lidar RFU
		Eigen::Vector3d objectPositionInCarBackFRU;
		m_sensorAxisTransformer.utm2CarBackRFU(objectPositionUTM,selfCarEulerXYZDegree,selfCarUTMAsTranslation, objectPositionInCarBackFRU);
		
		cloudObject.id = atoi(cloudpant.Id.c_str());
		cloudObject.x = objectPositionInCarBackFRU[0];
		cloudObject.y = objectPositionInCarBackFRU[1];
		cloudObject.z = objectPositionInCarBackFRU[2];

		cloudObject.longtitude = cloudpant.longitude;
		cloudObject.latitude = cloudpant.latitude;
		cloudObject.altitude = 0;
		
		
		float cloudObjectAngleDegree = cloudpant.courseAngle * 180.0 / M_PI;
		float cloudObjectAngleDegreeInLidarRFU_Clockwise = (cloudObjectAngleDegree - m_gpsMsg.heading);
		cloudObjectAngleDegreeInLidarRFU_Clockwise = cloudObjectAngleDegreeInLidarRFU_Clockwise < 0? cloudObjectAngleDegreeInLidarRFU_Clockwise + 360:cloudObjectAngleDegreeInLidarRFU_Clockwise;
		cloudObjectAngleDegreeInLidarRFU_Clockwise = cloudObjectAngleDegreeInLidarRFU_Clockwise >= 360 ? cloudObjectAngleDegreeInLidarRFU_Clockwise - 360:cloudObjectAngleDegreeInLidarRFU_Clockwise;

		// TODO 验证角度与相对速度: need lidar axis angle
		cloudObject.relspeedx = cloudpant.speed * sin(cloudObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0) - m_selfCarSpeed[0];
		cloudObject.relspeedy = cloudpant.speed * cos(cloudObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0) - m_selfCarSpeed[1];
		cloudObject.azimuth = cloudObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0;
		cloudObject.width = cloudpant.width;
		cloudObject.length = cloudpant.length;
		cloudObject.height = cloudpant.height;
		cloudObject.classification = static_cast<unsigned char>(transCloudObjectType2CameraDetectionType(cloudpant.vehicletype));
		cloudObject.value = static_cast<uint8_t>(COMMON::SensorType::CLOUDPLATFORM);
		
		//生成8角点
		float headingAnticlockwise =  2 * M_PI - cloudObject.azimuth; //
		vector<double> boxInfo = {cloudObject.x,cloudObject.y, objectPositionInCarBackFRU[2],
		                         cloudpant.length, cloudpant.width, cloudpant.height, headingAnticlockwise};
		vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
		for(const auto& singleCornerPointVector : eightCornerPoints){
			common_msgs::point3d singleCornerPoint;
			singleCornerPoint.x = singleCornerPointVector[0];
			singleCornerPoint.y = singleCornerPointVector[1];
			singleCornerPoint.z = singleCornerPointVector[2];
			
			cloudObject.points.emplace_back(std::move(singleCornerPoint));
		}
		cloudObjects.obs.emplace_back(std::move(cloudObject));
	}
}


int CarFusion::transCloudObjectType2CameraDetectionType(const int& cloudObjectType){
	int cameraClassfication = COMMON::LidarDetectionClassification::Unknown;
	switch(cloudObjectType){
		case 0:
		case 9:
			return COMMON::LidarDetectionClassification::Unknown;
		case 4:
			return  COMMON::LidarDetectionClassification::Pedestrian;
		case 5:
			return  COMMON::LidarDetectionClassification::Bicycle;
		case 6: // 摩托车认为是三轮车
			return  COMMON::LidarDetectionClassification::Tricycle;
		case 1:
		case 2:
		case 3:
		case 7:
		case 8:
		case 10:
		case 11:
		default:
			return  COMMON::LidarDetectionClassification::Car;
	}
}
void CarFusion::objectFusion(){
	m_fusionObjectMsg.obs.clear();
	
	m_fusionObjectMsg = m_lidarMsg;
	m_pLogger->info("lidarObject size: {}, cloudobject size: {}",  m_fusionObjectMsg.obs.size(), m_cloudObjectMsg.obs.size());
	for (auto& cloudobject: m_cloudObjectMsg.obs){
		m_fusionObjectMsg.obs.emplace_back(cloudobject);
	}
	m_pLogger->info("all Object: {}",  m_fusionObjectMsg.obs.size());
}



void CarFusion::publishFusionObject(){
	pub_fusionObject->publish(m_fusionObjectMsg);  //发布结果 "track_results",使用多边形点进行规划
	m_pLogger->info("finished object published");
}


#endif // ROS1_FOUND


#ifdef ROS2_FOUND 
CarFusion::CarFusion(rclcpp::Node::SharedPtr nodeHandle){
	m_nh = nodeHandle;
	m_qos_profile.reliable();
	m_qos_profile.avoid_ros_namespace_conventions(true);

	m_gpstime = 0;
	m_curLidarStamp = 0;
	
	m_lidar2carBackRFU_X = 0;
	m_lidar2carBackRFU_Y = 0;
	m_lidar2carBackRFU_Z = 0;
	
	m_lidarFLU2carBackRFU_rollDegree = 0;
	m_lidarFLU2carBackRFU_pitchDegree = 0;
	m_lidarFLU2carBackRFU_yawDegree = 0;
	
	m_lidarRFU2carBackRFU_rollDegree = 0;
	m_lidarRFU2carBackRFU_pitchDegree = 0;
	m_lidarRFU2carBackRFU_yawDegree = 0;
	
	m_lidarRFU2carBackRFU_eulerXYZDegree = Eigen::Vector3d::Zero();
	m_lidarFLU2carBackRFU_eulerXYZDegree = Eigen::Vector3d::Zero();
	m_lidar2carBackRFU_translation = Eigen::Vector3d::Zero();
	
	memset(&m_lidarMsg, 0, sizeof(m_lidarMsg));
	memset(&m_fusionObjectMsg, 0, sizeof(m_fusionObjectMsg));
	
	m_lidarMsg.obs.clear();
	m_fusionObjectMsg.obs.clear();

	m_selfCarSpeed.reserve(3);
	
	std::string nodeName = m_nh->get_name(); //获取节点名称
	int nameIndex = nodeName.find_first_not_of("/");
	std::string nodeNameNoChar = nodeName.substr(nameIndex); //得到"sensrorradar"
	std::string nodePath = ament_index_cpp::get_package_share_directory(nodeNameNoChar);
	std::string packagePath = nodePath + "/launch/params.yaml";
	m_pConfigManager = boost::make_shared<ConfigManager::ConfigManager>(packagePath);

	m_pcLogger = std::make_shared<Logger>(m_pConfigManager);
	m_pLogger = m_pcLogger->getLogger();

	m_pConfigManager->printParams(m_pLogger);

	// 检查日志记录器是否为异步类型
    if (typeid(*m_pLogger) == typeid(spdlog::async_logger)) {
        m_pLogger->info("Asynchronous logging is enabled.");
    } else {
        m_pLogger->info("Asynchronous logging is not enabled.");
    }

	m_pLogger->info("nodeName: {}", nodeName);
	m_pLogger->info("nodePath: {}", nodePath);
	m_pLogger->info("packagePath: {}", packagePath);

	m_nh->declare_parameter(m_pConfigManager->m_carName + "/lidar2carBackRFU_X",m_lidar2carBackRFU_X); //
	m_nh->declare_parameter(m_pConfigManager->m_carName + "/lidar2carBackRFU_Y",m_lidar2carBackRFU_Y); //
	m_nh->declare_parameter(m_pConfigManager->m_carName + "/lidar2carBackRFU_Z",m_lidar2carBackRFU_Z); //
	m_nh->declare_parameter(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_rollDegree",m_lidarFLU2carBackRFU_rollDegree); //
	m_nh->declare_parameter(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_pitchDegree",m_lidarFLU2carBackRFU_pitchDegree); //
	m_nh->declare_parameter(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_yawDegree",m_lidarFLU2carBackRFU_yawDegree); //
	m_nh->declare_parameter(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_rollDegree",m_lidarRFU2carBackRFU_rollDegree); //
	m_nh->declare_parameter(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_pitchDegree",m_lidarRFU2carBackRFU_pitchDegree); //
	m_nh->declare_parameter(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_yawDegree",m_lidarRFU2carBackRFU_yawDegree); //
	
	m_nh->get_parameter(m_pConfigManager->m_carName + "/lidar2carBackRFU_X",m_lidar2carBackRFU_X); //
	m_nh->get_parameter(m_pConfigManager->m_carName + "/lidar2carBackRFU_Y",m_lidar2carBackRFU_Y); //
	m_nh->get_parameter(m_pConfigManager->m_carName + "/lidar2carBackRFU_Z",m_lidar2carBackRFU_Z); //
	m_nh->get_parameter(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_rollDegree",m_lidarFLU2carBackRFU_rollDegree); //
	m_nh->get_parameter(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_pitchDegree",m_lidarFLU2carBackRFU_pitchDegree); //
	m_nh->get_parameter(m_pConfigManager->m_carName + "/lidarFLU2carBackRFU_yawDegree",m_lidarFLU2carBackRFU_yawDegree); //
	m_nh->get_parameter(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_rollDegree",m_lidarRFU2carBackRFU_rollDegree); //
	m_nh->get_parameter(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_pitchDegree",m_lidarRFU2carBackRFU_pitchDegree); //
	m_nh->get_parameter(m_pConfigManager->m_carName + "/LidarRFUAxis2CarBackRFU_yawDegree",m_lidarRFU2carBackRFU_yawDegree); //
	

	m_pLogger->info("carName: {}", m_pConfigManager->m_carName);
	m_pLogger->info("Copyright©2021-2023 VANJEE Technology. All rights reserved");

	
	m_lidarRFU2carBackRFU_eulerXYZDegree << m_lidarRFU2carBackRFU_rollDegree, m_lidarRFU2carBackRFU_pitchDegree, m_lidarRFU2carBackRFU_yawDegree;
	m_lidarFLU2carBackRFU_eulerXYZDegree << m_lidarFLU2carBackRFU_rollDegree, m_lidarFLU2carBackRFU_pitchDegree, m_lidarFLU2carBackRFU_yawDegree;
	m_lidar2carBackRFU_translation << m_lidar2carBackRFU_X, m_lidar2carBackRFU_Y, m_lidar2carBackRFU_Z;
	

	if(m_pConfigManager->m_isSaveTimeUseFile){
		m_pTimeUseFileWriter = boost::make_shared<FileWriter>(m_pConfigManager);
		std::string timestampStr = std::to_string(long(m_nh->now().seconds() * 1000)) + ".csv";
		m_pTimeUseFileWriter->createFile(timestampStr);
		std::string dataHeader = "frameCount,trackedObjectSize,trackingTimeUse,allLidarOBUObjectSize,allLidarOBUTimeUse\n";
		m_pTimeUseFileWriter->writeHeader(dataHeader);
	}
	

	//订阅话题
	sub_apolloObjects = m_nh->create_subscription<common_msgs_humble::msg::Sensorobjects>("/apollo/tracked_objects",10,
		[this](const common_msgs_humble::msg::Sensorobjects::SharedPtr msg) {
			CarFusion::SubCallback_apolloObjects(msg);
		});
	sub_gps = m_nh->create_subscription<common_msgs_humble::msg::Sensorgps>("/sensorgps", 2000, 
		[this](const common_msgs_humble::msg::Sensorgps::SharedPtr msg) {
			CarFusion::SubCallback_gps(msg);
		});
	
	sub_cloudobjects = m_nh->create_subscription<common_msgs_humble::msg::Cloudpants>("/cloud/cloudobjects", 10, 
		[this](const common_msgs_humble::msg::Cloudpants::SharedPtr msg) {
			CarFusion::SubCallback_cloudpants(msg);
		});

	//发布话题
	pub_lidarObjectBBX = m_nh->create_publisher<visualization_msgs::msg::MarkerArray>(nodeName + "/lidarObjectBBX",10);
	pub_cloudObjectsBBX = m_nh->create_publisher<visualization_msgs::msg::MarkerArray>(nodeName + "/cloudObjectsBBX", 10);
	pub_fusionObjectBBX = m_nh->create_publisher<visualization_msgs::msg::MarkerArray>(nodeName + "/fusionObjectBBX", 10);
	pub_fusionObject = m_nh->create_publisher<common_msgs_humble::msg::Sensorobjects>("/objectTrack/track_results8CornerForRviz",10);
	pub_fusiontrackingElapsedtime = m_nh->create_publisher<common_msgs_humble::msg::Elapsedtime>(nodeName + "/elapsedtime",10);

}

CarFusion::~CarFusion(){
	m_pConfigManager = nullptr;
	m_pcLogger = nullptr;
	m_pLogger = nullptr;
};

void CarFusion::run() {
	rclcpp::Rate rate(30);
	
	while(rclcpp::ok()) {
		rclcpp::spin_some(m_nh);
		if(!m_pConfigManager->m_isUseRosBag){
			fusion();
		}
		rate.sleep();
	}

}

void CarFusion::fusion() {
	if(m_pConfigManager->m_isSimulateMode)
		return;

	//// 以lidar为主传感器,必须以lidar数据开始，如果第一帧是radar或者其他传感器退出
	m_lidarMsg.obs.clear();
	
	std::unique_lock<std::mutex> lidarLock(m_lidarMutex);
	if(!m_lidarMsgDeque_.empty()) {
		// std::lock_guard<std::mutex> lidarLock(m_lidarMutex);
		m_lidarMsg = m_lidarMsgDeque_.front();
		m_lidarMsgDeque_.pop_front();
		m_pLogger->info("m_lidarMsgDeque_ size: {}", m_lidarMsgDeque_.size());
	}
	else{
		m_pLogger->warn("m_lidarMsgDeque_ is empty");
		return;
	}
	lidarLock.unlock();

	static int lidarFrameCount = 0;
	m_pLogger->info("\n\n第 {} 帧lidar融合过程...................................", lidarFrameCount++);

	long curStartStamp = m_nh->now().seconds() * 1000;
	float timeGap = curStartStamp - m_lidarMsg.timestamp;
	m_pLogger->info("curStartROSTimestamp = {:.3f}, m_lidarMsg timestamp = {:.3f}, timeGap(ms) = {:.3f}", curStartStamp / 1000.0, m_lidarMsg.timestamp / 1000.0, timeGap);
	if(!m_lidarMsgDeque_.empty()){
		m_pLogger->info("m_lidarMsgDeque_.front() timestamp = {:.3f}", m_lidarMsgDeque_.front().timestamp / 1000.0);
	}
	else{
		m_pLogger->info("m_lidarMsgDeque_.empty()");
	}
	m_curLidarStamp = m_lidarMsg.timestamp;

	long getLidarEndTime =  m_nh->now().seconds()*1000;
	double getLidarTimeUse = (double)(getLidarEndTime - curStartStamp);
	m_pLogger->info("[getLidarTimeUse use time(ms)] = {}", getLidarTimeUse);

	//// lidar-GPS-radar-cloudObject时间同步
	sensorTimeSynchro();

	m_selfCarUTMPosition.clear();
	
	//TODO TEST
	// {
	// 	WGS84Corr lla;//填充经纬度
	// 	m_pLogger->info("lon0000: {:.7f}, lat: {:.7f}",  m_gpsMsg.lon, m_gpsMsg.lat);
	// 	m_wgs84Utm.UTMXYToLatLon(m_gpsMsg.lon, m_gpsMsg.lat, m_pConfigManager->m_cityUTMCode, false, lla);
	// 	m_gpsMsg.lon = lla.log / M_PI * 180;
	// 	m_gpsMsg.lat = lla.lat / M_PI * 180;
		
	// 	m_pLogger->info("citycode1111: {}",  m_pConfigManager->m_cityUTMCode);
	// 	m_pLogger->info("lon1111: {:.7f}, lat: {:.7f}",  m_gpsMsg.lon, m_gpsMsg.lat);
	// }
	m_selfCarUTMPosition = m_wgs84Utm.getUTMPosition(m_gpsMsg.lon, m_gpsMsg.lat, m_gpsMsg.alt);

	//设置m_sensorAxisTransformer公共数据
	Eigen::Vector3d selfCarEulerDegrees{m_gpsMsg.pitch, m_gpsMsg.roll, m_gpsMsg.heading};
	m_sensorAxisTransformer.setSelfCarEulerDegrees(selfCarEulerDegrees);
	m_sensorAxisTransformer.setSelfCarUTMPosition(m_selfCarUTMPosition);

	m_selfCarSpeed.clear();
	//第一辆车发出NED,看做是ENU顺序,三轴角度和速度都看作是ENU
	m_selfCarSpeed = m_sensorAxisTransformer.ENU2CarBackRFUAxis(Eigen::Vector3d{m_gpsMsg.pitch, m_gpsMsg.roll, m_gpsMsg.heading},
	                                                            Eigen::Vector3d{m_gpsMsg.speed_e, m_gpsMsg.speed_n, -m_gpsMsg.speed_d});


	
	apolloObjectsPreprocess();
	sensorobjectsBoxShow(m_lidarMsg, COMMON::SensorType::LIDAR);
	

	// cloudObjects预处理
	cloudpantsPreprocess(m_cloudpantsMsg, m_cloudObjectMsg);
	sensorobjectsBoxShow(m_cloudObjectMsg, COMMON::SensorType::CLOUDPLATFORM);
	
	////目标融合
	m_gpstime = m_lidarMsg.timestamp;

	
	long preprocessEndTime =  m_nh->now().seconds()*1000;
	m_pLogger->info("开始融合.......");
	objectFusion();
	sensorobjectsBoxShow(m_fusionObjectMsg, COMMON::SensorType::FUSION);
	long fusionEndTime =  m_nh->now().seconds()*1000;
	double fusionTimeUse = (double)(fusionEndTime - preprocessEndTime);
	m_pLogger->info("[fusion use time(ms)] = {}", fusionTimeUse);
	
	publishFusionObject();

	common_msgs_humble::msg::Elapsedtime fusiontrackingElapsedtimeMsg;
	fusiontrackingElapsedtimeMsg.time = fusionTimeUse;
	pub_fusiontrackingElapsedtime->publish(fusiontrackingElapsedtimeMsg);
	
	static double maxFusionTrackingTimeUse = 0;
	if(fusionTimeUse > maxFusionTrackingTimeUse){
		maxFusionTrackingTimeUse = fusionTimeUse;
		m_pLogger->warn("get max time: : maxFusionTrackingTimeUse(ms) = {}", maxFusionTrackingTimeUse);
	}

	m_pLogger->info("max carfusion use time(ms) = {}", maxFusionTrackingTimeUse);
}
/***
 * 订阅lidar检测话题
 * @param msg lidar检测目标
 */
void CarFusion::SubCallback_apolloObjects(const common_msgs_humble::msg::Sensorobjects::SharedPtr &msg){
	static double preStamp = -1;
	// m_pLogger->info("Entering callback, preStamp = {:.3f}, current msg timestamp = {:.3f}", preStamp, msg->timestamp / 1000.0);
	if(preStamp < 0){
		preStamp = msg->timestamp;
		m_pLogger->info("callback first msg timestamp = {:.3f}", msg->timestamp / 1000.0);
	}
	else{
		float timeGap = msg->timestamp - preStamp;
		// if(timeGap >= 200){
		// 	m_pLogger->warn("检测丢帧: callback cur timestamp = {:.3f}, pre timestamp = {:.3f}, timeGap(ms) = {:.3f}",msg->timestamp / 1000.0, preStamp / 1000.0, timeGap);
		// }
		preStamp = msg->timestamp;
	}
	if(m_pConfigManager->m_isUseRosBag){
		double curtimestamp = m_nh->now().seconds() * 1000.0;
		float timeGap = curtimestamp - msg->timestamp;
		m_pLogger->info("sensorlidar2 callback current ros time = {:.3f}, msg timestamp = {:.3f}, timeGap(ms) = {:.3f}",curtimestamp/ 1000.0, msg->timestamp / 1000.0, timeGap);
		std::unique_lock<std::mutex> lidarLock(m_lidarMutex);
		m_lidarMsgDeque_.emplace_back(*msg);
		
		
		m_lidarMsg = *msg;
		apolloObjectsPreprocess();
		m_pLogger->info("apolloObjectsPreprocess");
		objectFusion();
		m_pLogger->info("objectFusion");
		sensorobjectsBoxShow(m_fusionObjectMsg, COMMON::SensorType::FUSION);
		m_pLogger->info("sensorobjectsBoxShow");
		publishFusionObject();
		m_pLogger->info("publishFusionObject");
		lidarLock.unlock();
	}
	else{
		double curtimestamp = m_nh->now().seconds() * 1000.0;
		float timeGap = curtimestamp - msg->timestamp;
		m_pLogger->info("sensorlidar2 callback current ros time = {:.3f}, msg timestamp = {:.3f}, timeGap(ms) = {:.3f}",curtimestamp / 1000.0, msg->timestamp / 1000.0, timeGap);
		// std::lock_guard<std::mutex> lidarLock(m_lidarMutex);
		std::unique_lock<std::mutex> lidarLock(m_lidarMutex);
		m_lidarMsgDeque_.emplace_back(*msg);
		lidarLock.unlock();
	}
}




void CarFusion::SubCallback_cloudpants(const common_msgs_humble::msg::Cloudpants::SharedPtr &msg){
	std::unique_lock<std::mutex> cloudpantsLock(m_cloudpantsMutex);
	if(!m_pConfigManager->m_isSimulateMode){// 实车测试
		m_cloudpantsMsgDeque_.emplace_back(*msg);
		if(m_cloudpantsMsgDeque_.size() > 100)
			m_cloudpantsMsgDeque_.pop_front();
	}
	else{// 仿真模式
		// cloudObjectPants 时间同步
		{
		  m_pLogger->info("virtualObject-gps time sync: ");
		  std::unique_lock<std::mutex> gpsLock(m_gpsMutex);
		  timeSynchro(m_gpsMsgDeque_, (*msg).timestamp, COMMON::SensorType::GPS);    //20221027 lidar检测与GPS信息的时间同步
		  if(!m_gpsMsgDeque_.empty()){
			  m_gpsMsg = m_gpsMsgDeque_.front();//20221027 lidar检测与GPS信息的时间同步
		  }
		  else{
			  m_pLogger->warn("WARNING: gps msg is empty, please check! ");
		  }
		  gpsLock.unlock();
		}
		m_selfCarSpeed.clear();
		m_pLogger->info("Car heading(deg) = {}", m_gpsMsg.heading);
		//第一辆车发出NED,看做是ENU顺序,三轴角度和速度都看作是ENU
		m_selfCarSpeed = m_sensorAxisTransformer.ENU2CarBackRFUAxis(Eigen::Vector3d{m_gpsMsg.pitch, m_gpsMsg.roll, m_gpsMsg.heading},
																	Eigen::Vector3d{m_gpsMsg.speed_e, m_gpsMsg.speed_n, -m_gpsMsg.speed_d});

		m_gpstime = m_gpsMsg.timestamp;//m_nh->now().seconds() * 1000;
		m_pLogger->info("use virtual object");
		// cloudObjects预处理
		cloudpantsPreprocess(*msg, m_cloudObjectMsg);
		sensorobjectsBoxShow(m_cloudObjectMsg, COMMON::SensorType::CLOUDPLATFORM);

		publishFusionObject();
	}
	cloudpantsLock.unlock();
}

/***
 * 订阅gps话题
 * @param msg gps话题
 */
void CarFusion::SubCallback_gps(const common_msgs_humble::msg::Sensorgps::SharedPtr &msg){
	// std::lock_guard<std::mutex> gpsLock(m_gpsMutex);
	std::unique_lock<std::mutex> gpsLock(m_gpsMutex);
	m_gpsMsgDeque_.emplace_back(*msg);
	if(m_gpsMsgDeque_.size() > 100)
		m_gpsMsgDeque_.pop_front();
	gpsLock.unlock();
}


void CarFusion::sensorTimeSynchro(){
	{
		m_pLogger->info("lidar-gps time sync:");
		// std::lock_guard<std::mutex> gpsLock(m_gpsMutex);
		std::unique_lock<std::mutex> gpsLock(m_gpsMutex);
		timeSynchro(m_gpsMsgDeque_, m_lidarMsg.timestamp, COMMON::SensorType::GPS);    //20221027 lidar检测与GPS信息的时间同步
		if(!m_gpsMsgDeque_.empty()){
			m_gpsMsg = m_gpsMsgDeque_.front();//20221027 lidar检测与GPS信息的时间同步
		}
		else{
			m_pLogger->warn("m_gpsMsgDeque_ is empty.");
		}
		gpsLock.unlock();
	}


	{
		m_pLogger->info("lidar-cloudpants time sync:");
		// std::lock_guard<std::mutex> cloudpantsLock(m_cloudpantsMutex);
		std::unique_lock<std::mutex> cloudpantsLock(m_cloudpantsMutex);
		m_cloudpantsMsg.pants.clear();
		timeSynchro(m_cloudpantsMsgDeque_, m_lidarMsg.timestamp, COMMON::SensorType::CLOUDPLATFORM);
		if(!m_cloudpantsMsgDeque_.empty()){
			m_cloudpantsMsg = m_cloudpantsMsgDeque_.front();
			m_cloudpantsMsgDeque_.pop_front();
			m_pLogger->info("cloudpants freameID = {}", m_cloudpantsMsg.frameid);
		}
		else{
			m_pLogger->warn("m_cloudpantsMsgDeque_ is empty.");
		}
		cloudpantsLock.unlock();
	}
}


/***
 * 20221027 lidar检测与传感器信息的时间同步 20220224 添加同步话题标志位
 * @tparam T 数据类型
 * @param msgDeque 需要同步的传感器（非主传感器）
 * @param curObjectFrameStamp 当前lidar检测时间
 * @param synchroFlag 同步标志位，1：lidar-gps，2：lidar-radar 3:lidar-obu
 */
template <typename T>
void CarFusion::timeSynchro(std::deque<T>& msgDeque,const int64& curObjectFrameStamp, const int synchroFlag){
	std::mutex dequeMutex;
	std::unique_lock<std::mutex> dequeLock(dequeMutex);

	if(msgDeque.empty()) {
		m_pLogger->warn("msgDeque empty,no need to syncchro.");
		return;
	}
	double curFrameStamp = (double)curObjectFrameStamp / 1000.0;

	//找到容器中数据时间戳小于同步时间的最近索引
	int curMsgIndex = 0;
	int msgDequeSize = msgDeque.size();
	for (int i = 0; i < msgDequeSize; ++i) {
		T curMsg = msgDeque[i];
		if(curMsg.timestamp / 1000.0  > curFrameStamp )
			break;
		curMsgIndex = i;
	}
	
	
	if(curMsgIndex + 1 == msgDeque.size()){//容器中数据时间戳都小于同步时间,只保留最后一个数据
		double lastMsgAndLidarDectionTimeGap = abs(msgDeque[curMsgIndex].timestamp / 1000.0 - curFrameStamp );
		if(curMsgIndex > 0){//等于0的情况 说明容器内第一帧radar数据就大于当前帧lidar数据，不处理
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	else{
		if(curMsgIndex + 1 <= msgDeque.size()){
			double lastMsgAndLidarDectionTimeGap = abs(msgDeque[curMsgIndex].timestamp / 1000.0 - curFrameStamp );
			double curMsgAndLidarDectionTimeGap = abs(msgDeque[curMsgIndex + 1].timestamp / 1000.0 - curFrameStamp);
			if(abs(curFrameStamp - msgDeque[curMsgIndex].timestamp / 1000.0) >//lidar前一帧
			   abs(curFrameStamp - msgDeque[curMsgIndex + 1].timestamp / 1000.0)){//lidar后一帧
				curMsgIndex += 1;//取最近的一帧数据
			}
			while(curMsgIndex--){
				msgDeque.pop_front();
			}
		}
	}
	float timeGap = curFrameStamp * 1000 - msgDeque.front().timestamp;
	static float minTimeGap = FLT_MAX, maxTimeGap = FLT_MIN;
    if (abs(timeGap) < abs(minTimeGap)) {
        minTimeGap = abs(timeGap);
    }
    if (abs(timeGap) > abs(maxTimeGap)) {
        maxTimeGap = abs(timeGap);
    }
   
	std::string l_sensorName = "UNKNOWN";
	auto it  = COMMON::SensorTypeMap.find(synchroFlag);
	if(it != COMMON::SensorTypeMap.end())
		l_sensorName = it->second;
	dequeLock.unlock();
	m_pLogger->info("lidar-{} 时间差(ms): {:.3f}, 当前msg时间 = {:.3f}, 当前lidar检测时间 = {:.3f}", 
					l_sensorName, timeGap, msgDeque.front().timestamp / 1000.0, curFrameStamp);
	m_pLogger->info("Min time gap: {:.3f}, Max time gap: {:.3f}", minTimeGap, maxTimeGap);
}


/***
 * 可视化目标msg，用于调试
 * @param msg_source 目标msg：carFrontRFU2CarFrontFLU
 */
void CarFusion::sensorobjectsBoxShow(const common_msgs_humble::msg::Sensorobjects &msg_source, const int& sensorType){
	std::string frameIDInfo = "car";

	visualization_msgs::msg::MarkerArray objectsMarkerArray;

	visualization_msgs::msg::Marker marker;
	marker.action=visualization_msgs::msg::Marker::DELETEALL;
	objectsMarkerArray.markers.emplace_back(marker);

	int msgSourceObsSize = msg_source.obs.size();
	int classColorSize = class_color.size();
	int speedSourceValueSize = v_speedSourceValue.size();
	vector<float> color{0.5, 0, 0};
	geometry_msgs::msg::Point p0, p1, p2, p3;
	geometry_msgs::msg::Point p4, p5, p6, p7;
	#pragma omp parallel for
	for (int i=0; i<msgSourceObsSize; i++){
		const auto& obs = msg_source.obs[i];
		int classification = int(obs.classification);

		// 距离目标不显示框
		if(classification == COMMON::LidarDetectionClassification::Unknown && m_pConfigManager->m_isUseRosBag == false)
			continue;

		if(classification < classColorSize){
			color = class_color[classification];
		}

		visualization_msgs::msg::Marker line_list_detect;
		double stamp = msg_source.timestamp / 1000.0;
		line_list_detect.header.frame_id  = frameIDInfo;
		//line_list_detect.header.stamp = ros::Time::now();
		line_list_detect.header.stamp = rclcpp::Time(stamp);//TODO  lidar时间戳 //ros::Time::now()
		line_list_detect.ns = "points_and_lines";
		line_list_detect.lifetime =rclcpp::Duration::from_seconds(1e9);//0.1
		line_list_detect.action = visualization_msgs::msg::Marker::ADD;
		line_list_detect.pose.orientation.w = 1.0;
		line_list_detect.id = i;
		line_list_detect.type = visualization_msgs::msg::Marker::LINE_LIST;
		//line width
		line_list_detect.scale.x = 0.1;
		//line_list_detect.scale.y = 0.1;
		//line_list_detect.scale.z = 0.1;
		//color green
		line_list_detect.color.a = 1;//透明度
		if(sensorType == COMMON::SensorType::LIDAR){
			line_list_detect.color.a = 1;//透明度
			line_list_detect.color.r = color[0]; //检测颜色
			line_list_detect.color.g = color[1];
			line_list_detect.color.b = color[2];
		}
		else if(sensorType == COMMON::SensorType::RADAR){
			line_list_detect.color.r = 0; //检测颜色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::OBU){
			line_list_detect.color.r = 0; //蓝色
			line_list_detect.color.g = 0;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::CLOUDPLATFORM){
			line_list_detect.color.r = 0; //检测颜色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 0;
		}
		else if(sensorType == COMMON::SensorType::FUSION){
			line_list_detect.color.a = 1;//透明度
			line_list_detect.color.r = 0; //检测颜色
			line_list_detect.color.g = 1;
			line_list_detect.color.b = 0;
		}
		else if(sensorType == COMMON::SensorType::FUSIONAllOBJECTS){
			line_list_detect.color.r = 1; //洋红色
			line_list_detect.color.g = 0;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::TRACKING){
			line_list_detect.color.r = 1; //洋红色
			line_list_detect.color.g = 0.5;
			line_list_detect.color.b = 1;
		}
		else if(sensorType == COMMON::SensorType::V2IFUSION){
			line_list_detect.color.r = 1; //洋红色
			line_list_detect.color.g = 0.5;
			line_list_detect.color.b = 1;
		}
		else{

		}


		int obj_class = int(msg_source.obs[i].classification);
		
		switch (sensorType) {
			case COMMON::SensorType::FUSION:
			{
				float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				vector<double> boxInfo = {obs.x,obs.y, obs.z,
										obs.length, obs.width, obs.height, headingAnticlockwise};
				vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				break;

				// const auto& points = obs.points;
				// p0.x = points[0].x;		p0.y = points[0].y;		p0.z = points[0].z;
				// p1.x = points[1].x;		p1.y = points[1].y;		p1.z = points[1].z;
				// p2.x = points[2].x;		p2.y = points[2].y;		p2.z = points[2].z;
				// p3.x = points[3].x;		p3.y = points[3].y;		p3.z = points[3].z;
				// p4.x = points[4].x;		p4.y = points[4].y;		p4.z = points[4].z;
				// p5.x = points[5].x;		p5.y = points[5].y;		p5.z = points[5].z;
				// p6.x = points[6].x;		p6.y = points[6].y;		p6.z = points[6].z;
				// p7.x = points[7].x;		p7.y = points[7].y;		p7.z = points[7].z;


				// p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y;		p0.z = msg_source.obs[i].points[0].z;
				// p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y;		p1.z = msg_source.obs[i].points[1].z;
				// p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y;		p2.z = msg_source.obs[i].points[2].z;
				// p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y;		p3.z = msg_source.obs[i].points[3].z;
				// p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y;		p4.z = msg_source.obs[i].points[4].z;
				// p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y;		p5.z = msg_source.obs[i].points[5].z;
				// p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y;		p6.z = msg_source.obs[i].points[6].z;
				// p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
			case COMMON::SensorType::TRACKING:
			{
				// float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				// vector<double> boxInfo = {obs.x,obs.y, obs.z,
				// 						obs.length, obs.width, obs.height, headingAnticlockwise};
				// vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				// p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				// p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				// p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				// p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				// p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				// p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				// p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				// p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				// break;

				const auto& points = obs.points;
				p0.x = points[0].x;		p0.y = points[0].y;		p0.z = points[0].z;
				p1.x = points[1].x;		p1.y = points[1].y;		p1.z = points[1].z;
				p2.x = points[2].x;		p2.y = points[2].y;		p2.z = points[2].z;
				p3.x = points[3].x;		p3.y = points[3].y;		p3.z = points[3].z;
				p4.x = points[4].x;		p4.y = points[4].y;		p4.z = points[4].z;
				p5.x = points[5].x;		p5.y = points[5].y;		p5.z = points[5].z;
				p6.x = points[6].x;		p6.y = points[6].y;		p6.z = points[6].z;
				p7.x = points[7].x;		p7.y = points[7].y;		p7.z = points[7].z;

				// p0.x = msg_source.obs[i].points[0].x;		p0.y = msg_source.obs[i].points[0].y;		p0.z = msg_source.obs[i].points[0].z;
				// p1.x = msg_source.obs[i].points[1].x;		p1.y = msg_source.obs[i].points[1].y;		p1.z = msg_source.obs[i].points[1].z;
				// p2.x = msg_source.obs[i].points[2].x;		p2.y = msg_source.obs[i].points[2].y;		p2.z = msg_source.obs[i].points[2].z;
				// p3.x = msg_source.obs[i].points[3].x;		p3.y = msg_source.obs[i].points[3].y;		p3.z = msg_source.obs[i].points[3].z;
				// p4.x = msg_source.obs[i].points[4].x;		p4.y = msg_source.obs[i].points[4].y;		p4.z = msg_source.obs[i].points[4].z;
				// p5.x = msg_source.obs[i].points[5].x;		p5.y = msg_source.obs[i].points[5].y;		p5.z = msg_source.obs[i].points[5].z;
				// p6.x = msg_source.obs[i].points[6].x;		p6.y = msg_source.obs[i].points[6].y;		p6.z = msg_source.obs[i].points[6].z;
				// p7.x = msg_source.obs[i].points[7].x;		p7.y = msg_source.obs[i].points[7].y;		p7.z = msg_source.obs[i].points[7].z;
				break;
			}
			case COMMON::SensorType::LIDAR:
			{
				float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				vector<double> boxInfo = {obs.x,obs.y, obs.z,
										obs.length, obs.width, obs.height, headingAnticlockwise};
				vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				break;
			}
			default:
			{
				// float headingAnticlockwise =  2 * M_PI - obs.azimuth;
				// vector<double> boxInfo = {obs.x,obs.y, obs.z,
				// 						obs.length, obs.width, obs.height, headingAnticlockwise};
				// vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
				// p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
				// p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
				// p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
				// p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
				// p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
				// p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
				// p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
				// p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
				// break;

				const auto& points = obs.points;
				p0.x = points[0].x;		p0.y = points[0].y;		p0.z = points[0].z;
				p1.x = points[1].x;		p1.y = points[1].y;		p1.z = points[1].z;
				p2.x = points[2].x;		p2.y = points[2].y;		p2.z = points[2].z;
				p3.x = points[3].x;		p3.y = points[3].y;		p3.z = points[3].z;
				p4.x = points[4].x;		p4.y = points[4].y;		p4.z = points[4].z;
				p5.x = points[5].x;		p5.y = points[5].y;		p5.z = points[5].z;
				p6.x = points[6].x;		p6.y = points[6].y;		p6.z = points[6].z;
				p7.x = points[7].x;		p7.y = points[7].y;		p7.z = points[7].z;
				break;
			}
		}

		//bottom
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p1);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p2);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p3);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p0);
		//top
		line_list_detect.points.push_back(p4); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p5); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p6); line_list_detect.points.push_back(p7);
		line_list_detect.points.push_back(p7); line_list_detect.points.push_back(p4);
		//side
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p4);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p7);
		//direction
		if(sensorType == COMMON::SensorType::TRACKING || sensorType == COMMON::SensorType::LIDAR || sensorType == COMMON::SensorType::FUSION){
			// 方向箭头
			geometry_msgs::msg::Point headingArrow1, headingArrow2, headingArrow3, headingArrow4;
			headingArrow1.x = (p4.x + p5.x)/2;
			headingArrow1.y = (p4.y + p5.y)/2;
			headingArrow1.z =  p4.z;
			
			headingArrow2.x = (p4.x*3 + p7.x)/4;
            headingArrow2.y = (p4.y*3 + p7.y)/4;
            headingArrow2.z =p4.z;
            line_list_detect.points.push_back(headingArrow1);
            line_list_detect.points.push_back(headingArrow2);

			headingArrow3.x = (p5.x*3 + p6.x)/4;
            headingArrow3.y = (p5.y*3 + p6.y)/4;
            headingArrow3.z =p6.z;
            line_list_detect.points.push_back(headingArrow1);
            line_list_detect.points.push_back(headingArrow3);

            headingArrow4.x = (p5.x + p4.x+ p7.x + p6.x)/4;
            headingArrow4.y = (p5.y + p4.y + p7.y + p6.y)/4;
            headingArrow4.z =p4.z;
            line_list_detect.points.push_back(headingArrow1);
            line_list_detect.points.push_back(headingArrow4);
		}
		


		// radar不画框，其他画框
		if(sensorType != COMMON::SensorType::RADAR){
			objectsMarkerArray.markers.push_back(line_list_detect);
		}
		visualization_msgs::msg::Marker text;
		text.header.frame_id = frameIDInfo;
		text.header.stamp = rclcpp::Time(stamp); //ros::Time::now()
		text.ns = "box";
		text.action = visualization_msgs::msg::Marker::ADD;
		text.lifetime =rclcpp::Duration::from_seconds(1e9);//0.1
		text.pose.orientation.w = 1;
		text.pose.position.x = msg_source.obs[i].x;
		text.pose.position.y = msg_source.obs[i].y;
		text.pose.position.z = msg_source.obs[i].z;
		text.id = i;
		text.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
		text.scale.z = 0.3;

		text.color.a = 1;//透明度
		text.color.r = 1;
		text.color.g = 1;
		text.color.b = 1;

		//添加目标横纵向速度信息
		std::string vxString = "", vyString = "";
		if(sensorType == COMMON::SensorType::RADAR){
			ostringstream str;
			str << std::setiosflags(std::ios::fixed) << std::setprecision(2) << msg_source.obs[i].relspeedx;
			vxString = str.str();
			str.str("");//清空数据
			str <<  msg_source.obs[i].relspeedy;
			vyString = str.str();
		}
		else{
			ostringstream str;
			str << std::setiosflags(std::ios::fixed) << std::setprecision(2) << msg_source.obs[i].relspeedx + m_selfCarSpeed[0];
			vxString = str.str();
			str.str("");//清空数据
			str <<  msg_source.obs[i].relspeedy + m_selfCarSpeed[1];
			vyString = str.str();
		}
		

		//添加跟踪类型
		//20221103 //0.初始目标 1.跟踪 2.lidar 3.lidar-radar-camera 4.融合中lidar-radar -20220908 20230111 简化
		int speedSourceValue = (int)msg_source.obs[i].value;
		std::string speedSource = speedSourceValue < speedSourceValueSize ? v_speedSourceValue[speedSourceValue] : v_speedSourceValue[speedSourceValueSize - 1];

		//添加目标运动状态
		//20220908 20230111 简化
		//std::string motionInfo = msg[i].motionInfo < v_motionInfo.size() ? v_motionInfo[msg[i].motionInfo] : v_motionInfo[v_motionInfo.size() - 1];
		std::string confidence = std::to_string(msg_source.obs[i].confidence);
		confidence = confidence.substr(0,4);
		string positionX = std::to_string(msg_source.obs[i].x);
		string positionY = std::to_string(msg_source.obs[i].y);
		// 显示：id
		// 第几次匹配成功-预测帧数
		// 类别-速度来源-运动信息
		// 横向速度-纵向速度
		// 置信度-驾驶意图-radarindex-radarobjectid
		switch (sensorType) {
			case COMMON::SensorType::LIDAR:
				text.text =
						"Lidar:\nID:" +std::to_string(msg_source.obs[i].id)
						+ "\nX:" + positionX + "\nY:" + positionY
                        + "\nlabel:" + std::to_string(msg_source.obs[i].classification)
						+ "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                        + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180.0 / M_PI)
						+ "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::RADAR:
				text.text = "Radar:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarindex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarobjectid);
				break;
			case COMMON::SensorType::OBU:
				text.text = "OBU:\nID:" +std::to_string(msg_source.obs[i].id)
							+ "\nX:" + positionX + "\nY:" + positionY
							+ "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
							+ "\nlabel:" + std::to_string(msg_source.obs[i].classification)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
							+ "\nabsVx:" + vxString + "\nabsVy:" + vyString
							+ "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180.0 / M_PI)
							+ "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::CLOUDPLATFORM:
				text.text = "Cloudplatform:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence;
				break;
			case COMMON::SensorType::FUSION:
				text.text = "Fusion:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
							+ "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarindex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarobjectid);
				break;
			case COMMON::SensorType::FUSIONAllOBJECTS:
				text.text = "FUSIONAllOBJECTS:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarindex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarobjectid);
				break;
			case COMMON::SensorType::TRACKING:
				text.text = "Tracking:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
                            //+ "-driving_intent:" + std::to_string(msg_source.obs[i].driving_intent)
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarindex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarobjectid);
				break;
			case COMMON::SensorType::V2IFUSION:
				text.text = "V2IFUSION:\nID:" +std::to_string(msg_source.obs[i].id)
				            + "\nlabel:" + std::to_string(msg_source.obs[i].classification) + "-value:" + std::to_string(msg_source.obs[i].value)
				            + "\nX:" + positionX + "\nY:" + positionY
				            + "\nL:" + std::to_string(msg_source.obs[i].length) + "\nW:" + std::to_string(msg_source.obs[i].width)
                            + "\nheading:" + std::to_string(msg_source.obs[i].azimuth * 180 / M_PI)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            + "\nconfidence:" + confidence
                            //+ "-driving_intent:" + std::to_string(msg_source.obs[i].driving_intent)
				            + "\nradarIndex:" + std::to_string(msg_source.obs[i].radarindex)
				            + "\nradarObjectID:" + std::to_string(msg_source.obs[i].radarobjectid);
				break;
			default:
				break;
		}
		objectsMarkerArray.markers.push_back(text);

		// ID 显示
		if(m_pConfigManager->m_isUseRosBag 
			&& (sensorType == COMMON::SensorType::TRACKING || sensorType == COMMON::SensorType::V2IFUSION)){
			visualization_msgs::msg::Marker objectsInfoMarker;
			objectsInfoMarker.header.frame_id = frameIDInfo;
			objectsInfoMarker.header.stamp = rclcpp::Time(stamp);
			objectsInfoMarker.ns = "ID";
			objectsInfoMarker.id = i;
			objectsInfoMarker.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
			objectsInfoMarker.lifetime = rclcpp::Duration::from_seconds(1e9);
			objectsInfoMarker.action = visualization_msgs::msg::Marker::ADD;

			objectsInfoMarker.pose.orientation.w = 1;
			geometry_msgs::msg::Point  p_temp;
			p_temp.x = (p4.x + p5.x)/2;
			p_temp.y = (p4.y + p5.y)/2;
			p_temp.z =  p4.z;
			objectsInfoMarker.pose.position.x = p_temp.x + 0.3*(p5.y-p4.y);
			objectsInfoMarker.pose.position.y = p_temp.y + 0.3*(p5.y-p4.y);
			objectsInfoMarker.pose.position.z = p_temp.z + 0.4*(p5.z-p4.z);

			objectsInfoMarker.scale.z = 2.4;
			objectsInfoMarker.color.a = 1;
			objectsInfoMarker.color.r = 1;
			objectsInfoMarker.color.g =1;
			objectsInfoMarker.color.b =0;
			objectsInfoMarker.text = std::to_string(msg_source.obs[i].id);
			objectsMarkerArray.markers.push_back(objectsInfoMarker);
		}
		
	}
	switch (sensorType) {
		case COMMON::SensorType::LIDAR:
			pub_lidarObjectBBX->publish(objectsMarkerArray);
			break;
		case COMMON::SensorType::CLOUDPLATFORM:
			pub_cloudObjectsBBX->publish(objectsMarkerArray);
			break;
		case COMMON::SensorType::FUSION:
			pub_fusionObjectBBX->publish(objectsMarkerArray); //
			break;
		default:
			break;
	}
	objectsMarkerArray.markers.clear();
}


void CarFusion::LatLonToLocalXY(double lon_car, double lat_car, float& x, float& y)
{
	const double R_e = 6378137.0;       // 椭球体长半轴
    const double R_f = 6356752.314245;  // 椭球体短半轴
    const double e_1 = sqrt(pow(R_e, 2) - pow(R_f, 2)) / R_e;   // 第一偏心率
    

    double sin_lat1 = sin(m_pConfigManager->m_hdMapLatitude * M_PI / 180.0);     	// sin of start lat
    double cos_lat1 = cos(m_pConfigManager->m_hdMapLatitude * M_PI / 180.0); 	    // sec of start lat
    double square_e = e_1 * e_1;
    double square_sin_lat1 = sin_lat1 * sin_lat1;

    double R_n = R_e / (sqrt(1 - square_e * square_sin_lat1));   				// 卯酉面等效曲率半径 (lon1, lat1)
    double R_m = (R_n * (1.0 - square_e)) / (1.0 - square_e * square_sin_lat1);   	// 子午面等效曲率半径 (lon1, lat1)

    x = (lon_car - m_pConfigManager->m_hdMapLongitude) * M_PI / 180.0 * R_n * cos_lat1;
    y = (lat_car - m_pConfigManager->m_hdMapLatitude) * M_PI / 180.0 * R_m;

}


void CarFusion::apolloObjectsPreprocess(){
	//lidar目标处理
	int lidarObjectsSize = m_lidarMsg.obs.size();
	m_pLogger->info("lidar obs raw size = {}", lidarObjectsSize);
	vector<double> boxInfo(7, 0);
	vector<vector<double>> pointsVector(8, vector<double>(3, 0.0));
	std::vector<double> objectSpeedInCarBackFRU(3);
	vector<double> objectUTM(3, 0);
	for(int i = 0; i < lidarObjectsSize; i++){
		common_msgs_humble::msg::Sensorobject& lidarObject = m_lidarMsg.obs[i];
		objectUTM = {lidarObject.x, lidarObject.y,lidarObject.z};
		m_pLogger->info("objectUTM: {:.7f}, lat: {:.7f}",  lidarObject.x, lidarObject.y);
		WGS84Corr lla;//填充经纬度
		m_wgs84Utm.UTMXYToLatLon(objectUTM[0], objectUTM[1],
								 m_pConfigManager->m_cityUTMCode, false, lla);

		Eigen::Vector3d inputUTMPosition{objectUTM[0] - m_selfCarUTMPosition[0],
										 objectUTM[1] - m_selfCarUTMPosition[1],
		                                  objectUTM[2]};
		std::vector<double> objectPositionInCarBackFRU = m_sensorAxisTransformer.ENU2BodyAxis(inputUTMPosition);
		

		// TODO 确认apollo的跟踪目标航向角，先按正北顺时针0~2pi计算
		float objectAngleDegreeInCarBackRFU_Clockwise = m_sensorAxisTransformer.NorthClockwise2CarBackRFU(lidarObject.azimuth * 180 / M_PI, m_gpsMsg.heading);
		float objectAngleRadInCarBackRFU_Clockwise = objectAngleDegreeInCarBackRFU_Clockwise * M_PI / 180.0;
		

		lidarObject.x = objectPositionInCarBackFRU[0];
		lidarObject.y = objectPositionInCarBackFRU[1];
		lidarObject.z = objectPositionInCarBackFRU[2];
		m_pLogger->info("lidarObject: {:.7f}, lat: {:.7f}",  lidarObject.x, lidarObject.y);
		lidarObject.longtitude = lla.log / M_PI * 180;
		lidarObject.latitude = lla.lat / M_PI * 180;
		lidarObject.altitude = 0;
		Eigen::Vector3d UTMRelativeSpeed{lidarObject.relspeedx, lidarObject.relspeedy, 0};
		objectSpeedInCarBackFRU = m_sensorAxisTransformer.ENU2BodyAxis(UTMRelativeSpeed);
		
		lidarObject.relspeedx = objectSpeedInCarBackFRU[0];//UTM速度转到ENU速度
		lidarObject.relspeedy = objectSpeedInCarBackFRU[1];
		lidarObject.azimuth = m_common.normalizeAngle(objectAngleRadInCarBackRFU_Clockwise * M_PI / 180.0); 

		lidarObject.value = COMMON::SensorType::LIDAR;
		lidarObject.radarindex = UINT8_MAX; // radarindex 未融合的目标设置索引为255
		lidarObject.radarobjectid = UINT8_MAX;
		//计算8角点
		float headingClockwise = 2.0 * M_PI - objectAngleRadInCarBackRFU_Clockwise; // 显示 -- 跟画框有关:画框需要逆时针，保存CSV需要顺时针，发送需要顺时针
		boxInfo = {lidarObject.x, lidarObject.y, lidarObject.z,
				  lidarObject.length, lidarObject.width, lidarObject.height,
		           headingClockwise};
		pointsVector = m_common.boxes_to_corners_3d(boxInfo);
		lidarObject.points.clear();
	
		for(const auto& pointVector:pointsVector){
			common_msgs_humble::msg::Point3d  cornerPoint;
			cornerPoint.x = pointVector[0];
			cornerPoint.y = pointVector[1];
			cornerPoint.z = pointVector[2];
			lidarObject.points.emplace_back(std::move(cornerPoint));
		}
		
	}
}

void CarFusion::cloudpantsPreprocess(const common_msgs_humble::msg::Cloudpants& cloudpants, common_msgs_humble::msg::Sensorobjects& cloudObjects){
	cloudObjects.obs.clear();
	cloudObjects.timestamp = cloudpants.timestamp;
	cloudObjects.gpstime = cloudpants.timestamp;
	cloudObjects.isvalid = 1;
	
	if(cloudpants.pants.empty()){
		m_pLogger->warn("cloudObjects is empty");
		return;
	}
	
	
	tagUTMCorr selfCarUTM;
	m_wgs84Utm.LatLonToUTMXY(m_gpsMsg.lat / 180.0 * M_PI, m_gpsMsg.lon / 180.0 * M_PI, selfCarUTM);
	Eigen::Vector3d selfCarEulerXYZDegree{m_gpsMsg.roll, m_gpsMsg.pitch, m_gpsMsg.heading};
	m_pLogger->info("m_gpsMsg.heading = {:.3f}", m_gpsMsg.heading);
	
	Eigen::Vector3d selfCarUTMAsTranslation{selfCarUTM.x, selfCarUTM.y, 0};
	m_pLogger->info("cloudpants object size = {}", cloudpants.pants.size());
	
	for (const auto& cloudpant:cloudpants.pants) {
		common_msgs_humble::msg::Sensorobject cloudObject;
		//	1. 经纬度转UTM
		tagUTMCorr utm;
		m_wgs84Utm.LatLonToUTMXY(cloudpant.latitude / 180.0 * M_PI, cloudpant.longitude / 180.0 * M_PI, utm);
		Eigen::Vector3d objectPositionUTM{utm.x, utm.y, 0};
		
		//2.UTM转lidar RFU
		Eigen::Vector3d objectPositionInCarBackFRU;
		m_sensorAxisTransformer.utm2CarBackRFU(objectPositionUTM,selfCarEulerXYZDegree,selfCarUTMAsTranslation, objectPositionInCarBackFRU);
		
		cloudObject.id = atoi(cloudpant.id.c_str());
		cloudObject.x = objectPositionInCarBackFRU[0];
		cloudObject.y = objectPositionInCarBackFRU[1];
		cloudObject.z = objectPositionInCarBackFRU[2];

		cloudObject.longtitude = cloudpant.longitude;
		cloudObject.latitude = cloudpant.latitude;
		cloudObject.altitude = 0;
		
		
		float cloudObjectAngleDegree = cloudpant.courseangle * 180.0 / M_PI;
		float cloudObjectAngleDegreeInLidarRFU_Clockwise = (cloudObjectAngleDegree - m_gpsMsg.heading);
		cloudObjectAngleDegreeInLidarRFU_Clockwise = cloudObjectAngleDegreeInLidarRFU_Clockwise < 0? cloudObjectAngleDegreeInLidarRFU_Clockwise + 360:cloudObjectAngleDegreeInLidarRFU_Clockwise;
		cloudObjectAngleDegreeInLidarRFU_Clockwise = cloudObjectAngleDegreeInLidarRFU_Clockwise >= 360 ? cloudObjectAngleDegreeInLidarRFU_Clockwise - 360:cloudObjectAngleDegreeInLidarRFU_Clockwise;

		// TODO 验证角度与相对速度: need lidar axis angle
		cloudObject.relspeedx = cloudpant.speed * sin(cloudObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0) - m_selfCarSpeed[0];
		cloudObject.relspeedy = cloudpant.speed * cos(cloudObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0) - m_selfCarSpeed[1];
		cloudObject.azimuth = cloudObjectAngleDegreeInLidarRFU_Clockwise * M_PI / 180.0;
		cloudObject.width = cloudpant.width;
		cloudObject.length = cloudpant.length;
		cloudObject.height = cloudpant.height;
		cloudObject.classification = static_cast<unsigned char>(transCloudObjectType2CameraDetectionType(cloudpant.vehicletype));
		cloudObject.value = static_cast<uint8_t>(COMMON::SensorType::CLOUDPLATFORM);
		
		//生成8角点
		float headingAnticlockwise =  2 * M_PI - cloudObject.azimuth; //
		vector<double> boxInfo = {cloudObject.x,cloudObject.y, objectPositionInCarBackFRU[2],
		                         cloudpant.length, cloudpant.width, cloudpant.height, headingAnticlockwise};
		vector<vector<double>> eightCornerPoints = m_common.boxes_to_corners_3d(boxInfo);
		for(const auto& singleCornerPointVector : eightCornerPoints){
			common_msgs_humble::msg::Point3d singleCornerPoint;
			singleCornerPoint.x = singleCornerPointVector[0];
			singleCornerPoint.y = singleCornerPointVector[1];
			singleCornerPoint.z = singleCornerPointVector[2];
			
			cloudObject.points.emplace_back(std::move(singleCornerPoint));
		}
		cloudObjects.obs.emplace_back(std::move(cloudObject));
	}
}


int CarFusion::transCloudObjectType2CameraDetectionType(const int& cloudObjectType){
	int cameraClassfication = COMMON::LidarDetectionClassification::Unknown;
	switch(cloudObjectType){
		case 0:
		case 9:
			return COMMON::LidarDetectionClassification::Unknown;
		case 4:
			return  COMMON::LidarDetectionClassification::Pedestrian;
		case 5:
			return  COMMON::LidarDetectionClassification::Bicycle;
		case 6: // 摩托车认为是三轮车
			return  COMMON::LidarDetectionClassification::Tricycle;
		case 1:
		case 2:
		case 3:
		case 7:
		case 8:
		case 10:
		case 11:
		default:
			return  COMMON::LidarDetectionClassification::Car;
	}
}



void CarFusion::objectFusion(){
	m_fusionObjectMsg.obs.clear();
	
	m_fusionObjectMsg = m_lidarMsg;
	m_pLogger->info("lidarObject size: {}, cloudobject size: {}",  m_fusionObjectMsg.obs.size(), m_cloudObjectMsg.obs.size());
	for (auto& cloudobject: m_cloudObjectMsg.obs){
		m_fusionObjectMsg.obs.emplace_back(cloudobject);
	}
	m_pLogger->info("all Object: {}",  m_fusionObjectMsg.obs.size());
}



void CarFusion::publishFusionObject(){
	pub_fusionObject->publish(m_fusionObjectMsg);  //发布结果 "track_results",使用多边形点进行规划
	m_pLogger->info("finished object published");
}

#endif // ROS2_FOUND 