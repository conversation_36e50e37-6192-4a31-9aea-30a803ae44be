<launch>
    <!-- <rosparam file="$(find fusiontracking)/launch/params.yaml" command="load"/> -->
    <!--   launch-prefix="gdb -ex run -args"      -->
    <!--  launch-prefix="valgrind -tool=memcheck -s -log-file=/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/performance/valReport.log -leak-check=full -show-reachable=yes -leak-resolution=low -track-origins=yes -undef-value-errors=yes"       -->
    <!--          launch-prefix="valgrind &#45;&#45;tool=callgrind &#45;&#45;callgrind-out-file=/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/performance/callgrind.out"-->
    <!-- launch-prefix="valgrind -read-var-info=yes -tool=helgrind -log-file=/mnt/data/autoDriving/HongQi2/autodriving0928/src/perception/outputMapping/performance/lock.txt" -->
    <node name="carfusion" pkg="carfusion" type="carfusion" output="screen" respawn="true"
        

    />
    <!-- <node name="carfusionrviz" pkg="rviz" type="rviz" args="-d $(find carfusion)/launch/carfusion.rviz"/> -->
</launch>