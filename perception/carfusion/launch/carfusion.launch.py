'''
Author: hanshuangquan <EMAIL>
Date: 2025-08-13 09:19:49
LastEditors: hanshuangquan <EMAIL>
LastEditTime: 2025-09-10 18:37:56
FilePath: /autodrivingVersionTest/src/perception/pluginfusion/launch/carfusion.launch.py
Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
'''
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare

def generate_launch_description():
    fusiontracking_package = FindPackageShare('fusiontracking')
    fusiontracking_paramsFile = PathJoinSubstitution([
    fusiontracking_package,
    'launch',
    'params.yaml'
    ])

    fusiontracking_node = Node(
        package='carfusion',
        executable='carfusion',
        name='carfusion',
        output='screen',
        respawn=True,
        parameters=[
            fusiontracking_paramsFile
        ]
    )

    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        output='screen',
        respawn=True,
        arguments=['-d', PathJoinSubstitution([fusiontracking_package, 'launch', 'carfusion.rviz'])]
    )

    return LaunchDescription([
        fusiontracking_node
        , 
        rviz_node
    ])
