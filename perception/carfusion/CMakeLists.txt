cmake_minimum_required(VERSION 3.5)
cmake_policy(SET CMP0057 NEW)
project(carfusion)

# set(CMAKE_BUILD_TYPE "Debug" )
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17 -march=native -O3")
## Compile as C++11, supported in ROS Kinetic and newer
# add_compile_options(-std=c++17)
#set(CMAKE_CXX_STANDARD 14)

find_package(rclcpp ) #REQUIRED
if(rclcpp_FOUND)
        message(=============================================================)
        message("-- ROS2 Found. ROS2 Support is turned On.")
        message(=============================================================)

        add_definitions(-DROS2_FOUND)
        include_directories(${rclcpp_INCLUDE_DIRS} include)

        find_package(ament_cmake REQUIRED)
        find_package(ament_index_cpp REQUIRED)
        find_package(std_msgs REQUIRED)
        find_package(sensor_msgs REQUIRED)
        find_package(geometry_msgs REQUIRED)
        find_package(common_msgs_humble REQUIRED)
        find_package(visualization_msgs REQUIRED)
        # 查找fmt库
        find_package(fmt REQUIRED)

        include_directories(
                /usr/include/
                src)
        link_directories(
                /usr/include/
                src)

        file(GLOB_RECURSE PLUGINFUSIONSORTFILE
                "src/carfusion.cpp"
                )
        add_executable(carfusion src/main.cpp ${PLUGINFUSIONSORTFILE})
        ament_target_dependencies(${PROJECT_NAME}
                rclcpp
                ament_index_cpp
                std_msgs
                sensor_msgs
                common_msgs_humble
                geometry_msgs
                visualization_msgs
                )



        target_include_directories(carfusion INTERFACE ${CMAKE_SOURCE_DIR}/perception/commonlibrary/src)
        # target_include_directories(carfusion PRIVATE ${CMAKE_SOURCE_DIR}/perception/commonlibrary/src)
        target_link_libraries(carfusion 
                ${PROJECT_SOURCE_DIR}/../commonlibrary/lib/libcommonlibrary.so
                fmt::fmt)

        install(TARGETS carfusion
        DESTINATION lib/${PROJECT_NAME}
        )

        install(DIRECTORY launch
        DESTINATION share/${PROJECT_NAME}
        )

        ament_export_include_directories(include)
        ament_export_dependencies(std_msgs)
        ament_package()

else()
find_package(catkin REQUIRED)
  if(roscpp_FOUND)
        message(=============================================================)
        message("-- ROS1 Found. ROS1 Support is turned On.")
        message(=============================================================)
        add_definitions(-DROS1_FOUND)
        find_package(catkin REQUIRED COMPONENTS
                roscpp
                rospy
                std_msgs
                common_msgs
                nav_msgs
                geometry_msgs
                roslib
        )


        find_package(PkgConfig REQUIRED)

        if(NOT DEFINED CMAKE_SUPPRESS_DEVELOPER_WARNINGS)
        set(CMAKE_SUPPRESS_DEVELOPER_WARNINGS 1 CACHE INTERNAL "No dev warnings")
        endif()


        catkin_package(
                # INCLUDE_DIRS include
                LIBRARIES ${PROJECT_NAME}
                catkin_depends roscpp rospy std_msgs

        )

        # 添加头文件
        include_directories(
                include
                ${catkin_INCLUDE_DIRS}
                /usr/include/
                src
        )

        #合并融合跟踪节点
        include_directories(
                include
                ${catkin_INCLUDE_DIRS}
                /usr/include/
                src
        )

        file(GLOB_RECURSE PLUGINFUSIONSORTFILE
                "src/carfusion.cpp"
                )
        add_executable(carfusion src/main.cpp ${PLUGINFUSIONSORTFILE})
        target_include_directories(carfusion INTERFACE ${CMAKE_SOURCE_DIR}/perception/commonlibrary/src)
        # target_include_directories(carfusion PRIVATE ${CMAKE_SOURCE_DIR}/perception/commonlibrary/src)
        target_link_libraries(carfusion ${catkin_LIBRARIES}
                ${CMAKE_SOURCE_DIR}/perception/commonlibrary/lib/libcommonlibrary.so)

        # Mark libraries for installation
        # See http://docs.ros.org/melodic/api/catkin/html/howto/format1/building_libraries.html
        install(TARGETS ${PROJECT_NAME}
                ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
                LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
                RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
                )

        # Mark cpp header files for installation
        install(DIRECTORY include
                DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
                FILES_MATCHING PATTERN "*.h"
                PATTERN ".svn" EXCLUDE
                )

        # Mark other files for installation (e.g. launch and bag files, etc.)
        install(DIRECTORY launch
                DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
                )
                
  endif(roscpp_FOUND)
  
endif(rclcpp_FOUND)
