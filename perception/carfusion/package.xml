<?xml version="1.0"?>
<package format="3">
  <name>carfusion</name>
  <version>0.0.0</version>
  <description>The carfusion package</description>

  <!-- One maintainer tag required, multiple allowed, one person per tag -->
  <!-- Example:  -->
  <!-- <maintainer email="<EMAIL>"><PERSON></maintainer> -->
  <maintainer email="<EMAIL>">wanji</maintainer>


  <!-- One license tag required, multiple allowed, one license per tag -->
  <!-- Commonly used license strings: -->
  <!--   BSD, MIT, Boost Software License, GPLv2, GPLv3, LGPLv2.1, LGPLv3 -->
  <license>TODO</license>


  <!-- Url tags are optional, but multiple are allowed, one per tag -->
  <!-- Optional attribute type can be: website, bugtracker, or repository -->
  <!-- Example: -->
  <!-- <url type="website">http://wiki.ros.org/readerjson</url> -->


  <!-- Author tags are optional, multiple are allowed, one per tag -->
  <!-- Authors do not have to be maintainers, but could be -->
  <!-- Example: -->
  <!-- <author email="<EMAIL>"><PERSON></author> -->


  <!-- The *depend tags are used to specify dependencies -->
  <!-- Dependencies can be catkin packages or system dependencies -->
  <!-- Examples: -->
  <!-- Use depend as a shortcut for packages that are both build and exec dependencies -->
  <!--   <depend>roscpp</depend> -->
  <!--   Note that this is equivalent to the following: -->
  <!--   <build_depend>roscpp</build_depend> -->
  <!--   <exec_depend>roscpp</exec_depend> -->
  <!-- Use build_depend for packages you need at compile time: -->
  <!--   <build_depend>message_generation</build_depend> -->
  <!-- Use build_export_depend for packages you need in order to build against this package: -->
  <!--   <build_export_depend>message_generation</build_export_depend> -->
  <!-- Use buildtool_depend for build tool packages: -->
  <!--   <buildtool_depend>catkin</buildtool_depend> -->
  <!-- Use exec_depend for packages you need at runtime: -->
  <!--   <exec_depend>message_runtime</exec_depend> -->
  <!-- Use test_depend for packages you need only for testing: -->
  <!--   <test_depend>gtest</test_depend> -->
  <!-- Use doc_depend for packages you need only for building documentation: -->
  <!--   <doc_depend>doxygen</doc_depend> -->
  <!-- ROS1 dependencies -->
  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>rosmsg</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>libpcl-all-dev</build_depend>
  <build_export_depend>roscpp</build_export_depend>
  <build_export_depend>rosmsg</build_export_depend>
  <build_export_depend>rospy</build_export_depend>
  <build_export_depend>libpcl-all-dev</build_export_depend>
  <exec_depend>roscpp</exec_depend>
  <exec_depend>rosmsg</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>libpcl-all-dev</exec_depend>
  <build_depend>message_generation</build_depend>
  <exec_depend>message_generation</exec_depend>
  <build_depend>message_runtime</build_depend>
  <exec_depend>message_runtime</exec_depend>
  <build_depend>roslib</build_depend>
  <exec_depend>roslib</exec_depend>
  <export>
  </export>


  <!-- ROS2 dependencies -->
  <!-- <buildtool_depend>ament_cmake</buildtool_depend>
  <member_of_group>rosidl_interface_packages</member_of_group>
  <buildtool_depend>rosidl_default_generators</buildtool_depend>
  <exec_depend>rosidl_default_runtime</exec_depend>
  <build_depend>rclcpp</build_depend>
  <exec_depend>rclcpp</exec_depend>
  <build_depend>rclpy</build_depend>
  <exec_depend>rclpy</exec_depend>
  <build_depend>std_msgs</build_depend>
  <exec_depend>std_msgs</exec_depend>

  <build_depend>sensor_msgs</build_depend>
  <exec_depend>sensor_msgs</exec_depend>

  <build_depend>pcl_conversions</build_depend>
  <exec_depend>pcl_conversions</exec_depend>
  <build_depend>pcl_ros</build_depend>
  <exec_depend>pcl_ros</exec_depend>
  <build_depend>libpcl-all-dev</build_depend>
  <exec_depend>libpcl-all</exec_depend>
  <build_depend>common_msgs_humble</build_depend>
  <exec_depend>common_msgs_humble</exec_depend>

  <build_depend>visualization_msgs</build_depend>
  <exec_depend>visualization_msgs</exec_depend>

  <exec_depend>ros2launch</exec_depend>

  <export>
   <build_type>ament_cmake</build_type>

  </export> -->


</package>
