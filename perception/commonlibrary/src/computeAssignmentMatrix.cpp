/********************************************************************************
* @author: s<PERSON><PERSON><PERSON><PERSON> han
* @date: 2022/7/4 上午9:43
* @version: 1.0
* @description: 
********************************************************************************/


#include "computeAssignmentMatrix.h"

#include "getiou.h"

	ComputeAssignmentMatrix::ComputeAssignmentMatrix()
	{
		// 锚点和track距离差、前后两帧锚点xy差、track速度方向夹角、box长宽差、点云数量、直方图距离差、重心、boxIOU
		//apollo：0.6f, 0.2f, 0.1f, 0.1f, 0.5f, 0.f, 0.f, 0.6f
		
		// 锚点和track中心点距离差、前后两帧锚点xy差、track速度方向夹角、box长宽差、点云数量、直方图距离差、重心、boxIOU
		distanceRatio_ = vector<float>{0.3f, // 锚点和track中心点距离差
									   0.0f, // 前后两帧锚点xy差
									   0.0f, // track速度方向夹角 directionDistance_
									   0.1f, // box长宽差
									   0.0f, // 点云数量
									   0.f, // 直方图距离差
									   0.f, // 重心
									   0.8f};// boxIOU
		meaningDelta_ = 1e-10f;
		
		
		boxCenterDistance_ = 0.0;
		boxCenterDistanceBetweenTwoFrame_ = 0.0;
		directionDistance_ = 0.0;
		boxSizeDistance_ = 0.0;
		pointNumDistance_ = 0.0;
		histogramDistance_ = 0.0;
		CentroidShiftDistance_ = 0.0;
		boxIOUDistance_ = 0.0;
		
		weightingDistance_ = 0.0f;
		matchParams_ = {0,0};
		
	}

	void ComputeAssignmentMatrix::setAssociateObject(const vector<float>& predictBox,
	                                                 const vector<float>& detectedBox){
		predictBox_ = predictBox;
		detectedBox_ = detectedBox;
	}

	void ComputeAssignmentMatrix::setPredictedObject(const vector<float>& predictBox){
		predictBox_ = predictBox;
	}

	void ComputeAssignmentMatrix::setDetectedObject(const vector<float>& detectedBox){
		detectedBox_ = detectedBox;
	}

	void ComputeAssignmentMatrix::setAssignmentMatrix(const int& firstObjectsSize, const int& secondObjectsSize){
		m_assignmentMatrix.clear();
		m_assignmentMatrix.resize(firstObjectsSize, vector<double>(secondObjectsSize, -1));
	}

	void ComputeAssignmentMatrix::setMatchParams(const std::vector<float>& matchParams){
		matchParams_ = matchParams;
	}
	

	//检测框和跟踪框中心点距离
	void ComputeAssignmentMatrix::locationDistance(const vector<float>& predictBox,
													const vector<float>& detectedBox,bool useApollo){
		//if(useApollo){
		//	//根据速度进行判断
		//	//predictBox: x y w l h yaw vx vy
		//	//tb.box : {msg.obs[i].x, msg.obs[i].y, float(msg.obs[i].width * 1.5), float(msg.obs[i].length*1.5), msg.obs[i].height, angel, vx, vy};
		//
		//	Eigen::Vector3f measuredPoint{lastTrackedObject.trackingBox_.box[0], lastTrackedObject.trackingBox_.box[1],lastTrackedObject.trackingBox_.box[4]};
		//	Eigen::Vector3d predictedPoint{predictBox[0],predictBox[1],predictBox[4]};
		//	Eigen::Vector3d measurePredictDiff = measuredPoint.cast<double>() - predictedPoint;
		//	// 检测观测中心点欧氏距离
		//	float locationDist = sqrt((measurePredictDiff.head(2).cwiseProduct(measurePredictDiff.head(2))).sum());
		//	Eigen::Vector2d ref_dir{lastTrackedObject.trackingBox_.box[6],predictBox[7]};//vx vy
		//	double speed = ref_dir.norm();//v
		//	ref_dir /= speed;// [cos,sin]
		//
		//	if(speed > 2){
		//		Eigen::Vector2d  ref_o_dir = Eigen::Vector2d(ref_dir[1], -ref_dir[0]);// 取负值为了将位移坐标系转到速度的坐标系下
		//		double dx = ref_dir(0) * measurePredictDiff(0) + ref_dir(1) * measurePredictDiff(1);//距离坐标系转到速度坐标系下，dx是速度的主方向
		//		double dy = ref_o_dir(0) * measurePredictDiff(0) + ref_o_dir(1) * measurePredictDiff(1);
		//		locationDist = sqrt(dx * dx * 0.5 + dy * dy * 2);//越相似，关联项值越小，故dx值的系数比dy小
		//	}
		//
		//	boxCenterDistance_ = std::atan(locationDist) / 2.0;  //转为【0-1】区间距离，距离越小，关联值约小,目标越相似，值域转到【0-1】区间
		//	std::cout<<"apollo1 boxCenterDistance_: "<<std::atan(locationDist) / 2.0<<",dist1: "<< locationDist <<std::endl;
		//}
		//else{
			//predictBox: x y w l h yaw vx vy
			//tb.box : {msg.obs[i].x, msg.obs[i].y, float(msg.obs[i].width * 1.5), float(msg.obs[i].length*1.5), msg.obs[i].height, angel, vx, vy};
			float deltaX = abs(predictBox[0] - detectedBox[0]);
			float deltaY = abs(predictBox[1] - detectedBox[1]);
			float distance = sqrt(pow(deltaX,2) + pow(deltaY,2));//距离越小，越可能是同一目标
			
			//boxCenterDistance_ = std::atan(distance) / 2.0;  //转为距离越大，值约小,值域转到【0-1】区间
			boxCenterDistance_ = distance;  //转为距离越大，值约小,值域转到【0-1】区间
			//std::cout<<"normal1 boxCenterDistance_: "<<std::atan(distance) / 2.0<<",dist1: "<< distance <<std::endl;
		//}
		
		//return boxCenterDistance_;
		
	}
	//20221014 检测框和跟踪框 椭圆门限距离
	void ComputeAssignmentMatrix::ellipseLocationDistance(){
		//matchParams_: 横向阈值3，纵向阈值6
		//predictBox: x y w l h yaw vx vy
		//tb.box : {msg.obs[i].x, msg.obs[i].y, float(msg.obs[i].width * 1.5), float(msg.obs[i].length*1.5), msg.obs[i].height, angel, vx, vy};
		//观测量旋转航向角
		float semiMajorAxis;     // 半长轴
   		float semiMinorAxis;     // 半短轴

		if(predictBox_[2] >= predictBox_[3]){
			semiMajorAxis = 2 * predictBox_[2];
			semiMinorAxis = 2 * predictBox_[3];

			//计算椭圆长短轴部分
			float tempX = pow(predictBox_[0] - detectedBox_[0],2) / pow(semiMajorAxis,2); //无法区分XY和长宽对应关系
			float tempY = pow(predictBox_[1] - detectedBox_[1],2) / pow(semiMinorAxis,2);
			ellipseLocationDistance_ = tempX + tempY;//距离越小，越可能是同一目标
		}
		else{
			semiMajorAxis= 2 * predictBox_[3];
			semiMinorAxis = 2 * predictBox_[2];
			//计算椭圆长短轴部分
			float tempX = pow(predictBox_[0] - detectedBox_[0],2) / pow(semiMinorAxis,2); //无法区分XY和长宽对应关系
			float tempY = pow(predictBox_[1] - detectedBox_[1],2) / pow(semiMajorAxis,2);
			ellipseLocationDistance_ = tempX + tempY;//距离越小，越可能是同一目标
		}
		
		// cout<<"ellipse distance = "<<ellipseLocationDistance_<<endl;
	}
	
	//方向差异:rpy
	void ComputeAssignmentMatrix::directionDistance(const vector<float>& predictBox, const vector<float>& detectedBox){
		Eigen::Vector3f predictionBoxAngle{0.0,0.0,predictBox[5]};//弧度
		Eigen::Vector3f detectedBoxAngle{0.0,0.0,detectedBox[5]};
		
		double directionDist = calculateCosTheta2DXY<float>(predictionBoxAngle,detectedBoxAngle);//cos，差异越小，评分越大
        //		std::cout<<"predictBox[5]: "<<predictBox[5]<<",  detectedBox[5]："<<detectedBox[5]<<std::endl;
        //		std::cout<<"directionDistance: "<<directionDist<<std::endl;
		directionDist = static_cast<float>(-directionDist) + 1.0f;//使用1-cos计算评分，差异越小，评分越小
		
		
		//float directionDist = sqrt(pow(predictBox[5] - detectedBox[5],2));
		
		directionDistance_ = directionDist;
		//std::cout<<"directionDistance_: "<<directionDistance_<<std::endl;
		//return directionDistance_;
	}
	
	//检测框和跟踪框尺寸-中心点坐标xyz
	void ComputeAssignmentMatrix::boxSizeDistance(const vector<float>& predictBox,const vector<float>& detectedBox){
		Eigen::Vector3f predictionBox{predictBox[0],predictBox[1],0.0};
		Eigen::Vector3f detectionBox{detectedBox[0],detectedBox[1],0.0};
		Eigen::Vector3f predictionBoxSize{predictBox[2],predictBox[3],0.0};//w l
		Eigen::Vector3f detectedBoxSize{detectedBox[2],detectedBox[3],0.0};
		
		float sizeDist = 0.0f;
		double dot_val_00 = fabs(predictionBox[0] * detectionBox[0] + predictionBox[1] * detectionBox[1]);
		double dot_val_01 = fabs(predictionBox[0] * detectionBox[1] - predictionBox[1] * detectionBox[0]);
		bool bbox_dir_close = dot_val_00 > dot_val_01;

		if(bbox_dir_close){
			float diff1 = fabs(predictionBoxSize[0] - detectedBoxSize[0]) / std::max(predictionBoxSize[0],
			                                                                         detectedBoxSize[0]);
			float diff2 = fabs(predictionBoxSize[1] - detectedBoxSize[1]) / std::max(predictionBoxSize[1],
			                                                                         detectedBoxSize[1]);
			sizeDist = std::min(diff1,diff2);
		}else{
			float diff1 = fabs(predictionBoxSize[0] - detectedBoxSize[1]) / std::max(predictionBoxSize[0],
			                                                                      detectedBoxSize[1]);
			float diff2 = fabs(predictionBoxSize[1] - detectedBoxSize[0]) / std::max(predictionBoxSize[1],
			                                                                         detectedBoxSize[0]);
			sizeDist = std::min(diff1,diff2);
		}
		//std::cout<<"sizeDist: "<<sizeDist<<std::endl;
		sizeDist = abs(sizeDist);
		boxSizeDistance_ = 1 - sizeDist / (sizeDist + 1);
		
		//sizeDist = abs(predictBox[2] / (double)detectedBox[2]) + abs(predictBox[3] / (double)detectedBox[3]);
		//std::cout<<"sizeDist: "<<sizeDist<<std::endl;
		//sizeDist = abs(sizeDist);
		//boxSizeDistance_ = sizeDist / (sizeDist + 1);
		
		
		//std::cout<<"boxSizeDistance_: "<<boxSizeDistance_<<std::endl;
		//return sizeDist;
	}
	
	//检测框和跟踪框宽长比 TODO
	
	//IOU,可选CuboidIoU或者runIOUCompute合并公共代码
	void ComputeAssignmentMatrix::boxIOUDistance(const vector<float>& predictBox,const vector<float>& detectedBox){
		// use 1-iou because the hungarian algorithm computes a minimum-cost assignment.
		//Eigen::MatrixXd truth_poses(1,9);
		
		//truth_poses<<pose{x,y,z}，三自由度的旋转{roll, pitch, yaw}，三自由度的尺寸{length,width,height}
		//cout<<"debug-boxIOUDistance-detect1: x = "<<predictBox[0]<<", y = " << predictBox[1] <<",l = " << predictBox[3] <<",  w = " << predictBox[4]<<endl;
		//truth_poses<<predictBox[0], predictBox[1], 1, 0, 0, predictBox[6], predictBox[3], predictBox[4], 1;//检测高度不准，所以这里高度使用1
		//truth_poses<<1, 1, 1, 0, 0, M_PI / 2.0 , 2, 2, 2;// test
		//Eigen::MatrixXd landmark_poses(1,9);
		//landmark_poses<<pose{x,y,z}，三自由度的旋转{roll, pitch, yaw}，三自由度的尺寸{length,width,height}
		//landmark_poses<<detectedBox[0], detectedBox[1], 1, 0, 0, detectedBox[6], detectedBox[3], detectedBox[4], 1;
		//landmark_poses<<1, 0.8, 0.8, 0, 0, M_PI / 3.0, 2, 2, 2; // test
		//cout<<"debug-boxIOUDistance-detect2: x = "<<detectedBox[0]<<", y = " << detectedBox[1] <<", l = " << detectedBox[3] <<",  w = " << detectedBox[4]<<endl;
		//float iou_3d = CuboidIoU(truth_poses, landmark_poses);//IOU越大，越可能是同一个目标
		float iou_3d = CuboidIoU(predictBox, detectedBox);//IOU越大，越可能是同一个目标
		// float iou_3d = runIOUCompute(truth_poses, landmark_poses);//IOU越大，越可能是同一个目标 //XXX 使用EIOU
		//std::cout<<"-------------------------rawIOUDistance_: "<<iou_3d<<std::endl;
		float iouDistance;
		iouDistance = 1 - iou_3d;//转换为求最小值，为了计算匹配对,iouDistance越小，越可能是同一个目标
		 //std::cout << "INFO: 3D IOU score is " << iou_3d << ", iouDistance = "<< iouDistance << ", predictBox[3] = "<<
         //           predictBox[3] << ",  predictBox[2] = "<<  predictBox[2]<< ",  detectedBox[3] = "<<  detectedBox[3]
			//	 << ",  detectedBox[2] = "<<  detectedBox[2]<< std::endl;
		iouDistance = std::min(1.0f, std::max(0.0f, iouDistance));
		boxIOUDistance_ = iouDistance;
		 //std::cout<<"-------------------------boxIOUDistance_: "<<boxIOUDistance_<<", iouDistance = " << iouDistance<<std::endl;
		//return boxIOUDistance_;//iouDistance;
		
		//2-diou计算，这里不适用
		//            iouMatrix[i][j] = 1 - GetIOU(predictBox, detectedBox);
		
	}
	
	void ComputeAssignmentMatrix::runComputeAssigenmentMatrix(){
		//locationDistance(predictBox_,detectedBox_, true);//
		locationDistance(predictBox_,detectedBox_, false);
		directionDistance(predictBox_,detectedBox_);
		boxSizeDistance(predictBox_,detectedBox_);
		boxIOUDistance(predictBox_,detectedBox_);
		//TODO  其他的关联项计算
		//std::cout<<"......................."<<std::endl;
	}
	
	float ComputeAssignmentMatrix::addDistanceWeighting(){
		if (boxSizeDistance_ > meaningDelta_){
			weightingDistance_ += distanceRatio_[0] * boxCenterDistance_;//20220926 修正关联项
		}
		if (boxCenterDistanceBetweenTwoFrame_ > meaningDelta_){
			weightingDistance_ += distanceRatio_[1] * boxCenterDistanceBetweenTwoFrame_;
		}
		if (directionDistance_ > meaningDelta_){
			weightingDistance_ += distanceRatio_[2] * directionDistance_;
		}
		if (boxSizeDistance_ > meaningDelta_){
			weightingDistance_ += distanceRatio_[3] * boxSizeDistance_;
		}
		if (pointNumDistance_ > meaningDelta_){
			weightingDistance_ += distanceRatio_[4] * pointNumDistance_;
		}
		if (histogramDistance_ > meaningDelta_){
			weightingDistance_ += distanceRatio_[5] * histogramDistance_;
		}
		if (CentroidShiftDistance_ > meaningDelta_){
			weightingDistance_ += distanceRatio_[6] * CentroidShiftDistance_;
		}
		if (boxIOUDistance_ > meaningDelta_){
			weightingDistance_ += distanceRatio_[7] * boxIOUDistance_;
		}
		
		return weightingDistance_;
	}
	
	
	
	float ComputeAssignmentMatrix::getLocationDistance(){
		locationDistance(predictBox_,detectedBox_, false);
		//locationDistance(predictBox_,detectedBox_, true);
		return boxCenterDistance_;
	}
	
	float ComputeAssignmentMatrix::getEllipseLocationDistance(){//20221014 椭圆门限距离
		ellipseLocationDistance();
		return ellipseLocationDistance_;
	}
	
	float ComputeAssignmentMatrix::getDirectionDistance(){
		directionDistance(predictBox_,detectedBox_);
		return directionDistance_;
	}
	float ComputeAssignmentMatrix::getBoxSizeDistance(){
		boxSizeDistance(predictBox_,detectedBox_);
		return boxSizeDistance_;
	}
	float ComputeAssignmentMatrix::getBoxIOUDistance(){
		//cout<<"-------------iou 3d before IOU = " << boxIOUDistance_ << endl;
		boxIOUDistance(predictBox_,detectedBox_);
		//cout<<"-------------iou 3d after IOU= " << boxIOUDistance_ << endl;
		return boxIOUDistance_;
	}
	float ComputeAssignmentMatrix::getWeightingDistance(){
		runComputeAssigenmentMatrix();
		
		weightingDistance_ = addDistanceWeighting();
		return weightingDistance_;
	}
	
	
	void ComputeAssignmentMatrix::resetDistance(){
		//TODO 释放成员变量lastTrackedObject_ predictBox_  detectedBox_
		boxCenterDistance_ = 0.0;
		boxCenterDistanceBetweenTwoFrame_ = 0.0;
		directionDistance_ = 0.0;
		boxSizeDistance_ = 0.0;
		pointNumDistance_ = 0.0;
		histogramDistance_ = 0.0;
		CentroidShiftDistance_ = 0.0;
		boxIOUDistance_ = 0.0;
		
		weightingDistance_ = 0.0f;
		ellipseLocationDistance_ = 0.0f;//20221014 椭圆门限距离
		
	}
	