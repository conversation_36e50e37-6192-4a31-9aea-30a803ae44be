/********************************************************************************
* @author: shuangquan han
* @date: 2023/7/7 上午9:24
* @version: 1.0
* @description: 
********************************************************************************/


#ifndef SRC_COMMON_H
#define SRC_COMMON_H

#include <iostream>
#include <vector>
#include <cmath>
#include <map>
#include <algorithm>

using namespace std;

/* FOREGROUND */
#define RST  "\x1B[0m"
#define KRED  "\x1B[31m"
#define KGRN  "\x1B[32m"
#define KYEL  "\x1B[33m"
#define KBLU  "\x1B[34m"
#define KMAG  "\x1B[35m"
#define KCYN  "\x1B[36m"
#define KWHT  "\x1B[37m"

#define FRED(x) KRED x RST //红色：错误
#define FGRN(x) KGRN x RST //绿色：重要信息提示
#define FYEL(x) KYEL x RST //黄色：重要警告提示
#define FBLU(x) KBLU x RST //蓝色：非重要信息提示
#define FMAG(x) KMAG x RST //洋红：非重要警告提示
#define FCYN(x) KCYN x RST //青色：调试信息
#define FWHT(x) KWHT x RST //白色
#define BOLD(x) "\x1B[1m" x RST //白色粗体
#define UNDL(x) "\x1B[4m" x RST //白色下划线

//用于显示不同目标的颜色
//红色：(1, 0, 0)
//绿色：(0, 1, 0)
//蓝色：(0, 0, 1)
//黄色：(1, 1, 0)
//青色：(0, 1, 1)
//洋红色：(1, 0, 1)
//橙色：(1, 0.5, 0)
//紫色：(0.5, 0, 1)
//青绿色：(0, 1, 0.5)
//淡红色：(1, 0.5, 0.5)

const static std::vector<std::vector<float>> class_color = {{1.0, 0.0, 0},{0.0, 1.0, 0},{0.0, 0.0, 1.0},
															{1.0, 1.0, 0},{0, 1.0, 1.0},{0.5, 0.0, 1.0},
															{0, 0.5, 1},{0.5, 0.5, 0},{0.5, 0, 0}};
//0.初始目标 1.跟踪 2.lidar 3.lidar-radar-camera 4.融合中lidar-radar
const static std::vector<std::string> v_speedSourceValue = {"0", "1", "L", "LRC", "R", "other", "error"};
//0.初始 1.动态目标 2.静态目标（静止的动态目标）3.静止目标（路锥等
const static std::vector<std::string> v_motionInfo = {"0", "D", "sD", "S", "error"};
const static std::vector<std::string> class_name = {"car", "bic", "bus", "tri", "ped", "cone", "tru", "unk", "zombiecar"};

struct Point {
    double lon, lat;
};


namespace COMMON {
	enum SensorType{
		LIDAR = 1,
		RADAR = 2,
		CAMERA = 3,
		GPS = 4,
		OBU = 5,
		CLOUDPLATFORM = 6,
		LIDAR_RADAR = 7,
		LIDAR_OBU = 8,
		LIDAR_RADAR_OBU = 9,
		FUSION = 10,
		TRACKING = 11,
		LIDARDETECTION = 12,
		FUSIONAllOBJECTS = 13,
		V2IFUSION = 14,
		V2ISHOW = 15,
		V2ISHOW1 = 16,
		V2ISHOW2 = 17,
		V2ISHOW3 = 18,
		V2NFUSION = 19
	};

	static const std::map<int, std::string> SensorTypeMap{
		{1,"LIDAR"}, {2,"RADAR"}, {3,"CAMERA"}, {4,"GPS"}, {5,"OBU"}, {6,"CLOUDPLATFORM"},
		{7,"LIDAR_RADAR"}, {8,"LIDAR_OBU"}, {9,"LIDAR_RADAR_OBU"},{10,"FUSION"},
		{11,"TRACKING"},{12,"LIDARDETECTION"},{13,"FUSIONAllOBJECTS"},{14,"V2IFUSION"},
		{15,"V2ISHOW"},{16,"V2ISHOW1"},{17,"V2ISHOW2"},{18,"V2ISHOW3"},{17,"V2NFUSION"},
		{18,"UNKNOWEN"}
		
	};
	
	enum LidarDetectionClassification{
		Car = 0,//virtual object change 0 to 2
		Bicycle = 1,
		Bus = 2,
		Tricycle = 3,
		Pedestrian = 4,//virtual object change 4 to 1
		Cone = 5,
		Truck = 6,
		Unknown = 7,
		Zombiecar = 100
	};

}

class Common {
public:
	Common();
	~Common();
	
	template<class T>
	vector<std::vector<T>> rotz(T t);

	template<class T>
	const T& clamp(const T& value, const T& min, const T& max);
	vector<vector<double>> boxes_to_corners_3d(const vector<double>& boxInfo);
	template <typename T>
	void coutVector(vector<T> nums){
		for (int j = 0; j < nums.size(); ++j) {
			cout<<" "<<nums[j]<<", ";
		}
		cout<<"\n";
	}

	bool isInStationRange(const double selfCarLat, const double selfCarLon,
	                              const double stationLat, const double stationLon,
	                              const double distanceFromStation);
	// 判断经纬度是否在区域内
	bool isPointInPolygon(const Point& point, const std::vector<Point>& polygon);

	bool isMotorVehicle(const int& classification);
	bool isNonMotorVehicle(const int& classification);
	bool isClusterObject(const int& classification);
	double normalizeAngle(const double& angleRad);
	float getTrimmedMean(std::vector<float>& lengths, float trimPercent);
	float caculateAverageAngle(const std::vector<float>& angles);

private:


};


#endif //SRC_COMMON_H
