/********************************************************************************
* @author: shuangquan han
* @date: 2023/7/7 上午9:24
* @version: 1.0
* @description: 
********************************************************************************/


#include "common.h"


Common::Common(){

}

Common::~Common(){

}

/***
 * 绕Z轴旋转函数t角度得到的旋转矩阵
 * @tparam T 数据类型（double或float）
 * @param t 旋转弧度（Rad）
 * @return 3*3旋转矩阵
 */
template<class T>
vector<std::vector<T>> Common::rotz(T t)
{
	T c = cos(t);
	T s = sin(t);
	std::vector<std::vector<T>> rotate_mat = {{c,  -s,  0}, {s,  c,  0}, {0, 0,  1}};
	return std::move(rotate_mat);
}

template<class T>
const T& Common::clamp(const T& value, const T& min, const T& max){
	return std::min(std::max(value, min), max);
}

/***
 * 获取目标8角点 坐标轴为RFU
 * @param boxInfo 目标信息，(N, 7) [x, y, z, length, width, high, heading-radian], (x, y, z) is the box center
 * @return 目标8角点,车头朝向 0-1-4-5前
 */
vector<vector<double>> Common::boxes_to_corners_3d(const vector<double>& boxInfo){
	/*
	 7 -------- 4
	/|         / |
	6 -------- 5 .
	| |        | |
	. 3 -------- 0
	|/   --|-》 y/
	2 -----x-- 1
	Args:
		boxes3d:  (N, 7) [x, y, z, length, width, high, heading], (x, y, z) is the box center
	Returns:
	*/
	
	double w_half = boxInfo[4] / 2.0;
	double l_half = boxInfo[3] / 2.0;
	double h_half = boxInfo[5] / 2.0;
	std::vector<double> position={boxInfo[0], boxInfo[1], boxInfo[2]};//xyz
	std::vector<std::vector<double>> R = rotz( boxInfo[6]);//yawRadian
	std::vector<std::vector<double>> corners = {// 车头朝向 0-1-4-5前
			{-w_half,  l_half,  -h_half},//0 宽为x
			{ w_half,  l_half,  -h_half},//1
			{ w_half, -l_half,  -h_half},//2
			{-w_half, -l_half,  -h_half},//3
			{-w_half,  l_half,   h_half},//4
			{ w_half,  l_half,   h_half},//5
			{ w_half, -l_half,   h_half},//6
			{-w_half, -l_half,   h_half}//7
	};
	// 生成8角点存入pointcorners
	vector<vector<double>> pointcorners(8, vector<double>(3));//
	for(size_t n=0;n<8;++n)
	{
		pointcorners[n][0] = R[0][0] * corners[n][0] + R[0][1] * corners[n][1] + R[0][2] * corners[n][2] + position[0];
		pointcorners[n][1] = R[1][0] * corners[n][0] + R[1][1] * corners[n][1] + R[1][2] * corners[n][2] + position[1];
		pointcorners[n][2] = R[2][0] * corners[n][0] + R[2][1] * corners[n][1] + R[2][2] * corners[n][2] + position[2];
	}
	return std::move(pointcorners);
}


/*
1.
函数参数 selfCarLon,selfCarLat 自车经纬度， stationLon，stationLat 基站经纬度

EARTH_RADIUS=6371.0;  //km
distanceFromStation 基站范围：单位为m
*/
bool Common::isInStationRange(const double selfCarLat, const double selfCarLon, 
							  const double stationLat, const double stationLon,
					   const double distanceFromStation)
{
	// double s_lon = 120.636348, s_lat = 31.422856;
	double radLat1 = (stationLat * M_PI / 180.0);  //s_lat-->stationLat
	double radLat2 = (selfCarLat * M_PI / 180.0);
	double a = radLat1 - radLat2;
	double b = (stationLon * M_PI / 180.0) - (selfCarLon * M_PI / 180.0);  // s_lon-->stationLon
	double l_dis = 2 * asin(sqrt(pow(sin(a/2),2) + cos(radLat1)*cos(radLat2)*pow(sin(b/2),2)));
	double EARTH_RADIUS = 6371.0;  //km
	l_dis = l_dis * EARTH_RADIUS * 1000;
	// std::cout<<"l_dis:"<<l_dis<<std::endl;
	if(distanceFromStation > l_dis)	{
		return true;   //车在基站100m范围内
	}
	else{
		return false;  //车在基站100m范围外
	}
}


bool Common::isPointInPolygon(const Point& point, const std::vector<Point>& polygon) {
		int i, j, c = 0;
		for (i = 0, j = polygon.size()-1; i < polygon.size(); j = i++) {
			if (((polygon[i].lat > point.lat) != (polygon[j].lat > point.lat)) &&
				(point.lon < (polygon[j].lon - polygon[i].lon) * (point.lat - polygon[i].lat) / (polygon[j].lat - polygon[i].lat) + polygon[i].lon))
				c = !c;
		}
		return c;
	}

bool Common::isMotorVehicle(const int& classification){
	bool isMotorVehicle = false;
	
	if(classification == COMMON::LidarDetectionClassification::Car
	                       || classification == COMMON::LidarDetectionClassification::Bus
	                       || classification == COMMON::LidarDetectionClassification::Truck
	                       || classification == COMMON::LidarDetectionClassification::Zombiecar){
				isMotorVehicle = true;		   
	}
	return isMotorVehicle;
}

bool Common::isNonMotorVehicle(const int& classification){
	bool isNonMotorVehicle = false;
	
	if(classification == COMMON::LidarDetectionClassification::Bicycle
	                       || classification == COMMON::LidarDetectionClassification::Tricycle
	                       || classification == COMMON::LidarDetectionClassification::Pedestrian
	                       || classification == COMMON::LidarDetectionClassification::Cone){
				isNonMotorVehicle = true;		   
	}
	return isNonMotorVehicle;
}

bool Common::isClusterObject(const int& classification){
	return classification == COMMON::LidarDetectionClassification::Unknown;
}


// 返回弧度值
double Common::normalizeAngle(const double& angleRad) {
    double angleRadNormalized = std::fmod(angleRad, 2.0 * M_PI); 
    if (angleRadNormalized < 0.0) {
        angleRadNormalized += (2.0 * M_PI);
    }
    return std::move(angleRadNormalized);
}


// 修剪平均数是去掉一定百分比的最高和最低值后计算的平均数。例如，去掉两端各5%的数据后再求平均。
// trimPercent = 0.1
float Common::getTrimmedMean(std::vector<float>& lengths, float trimPercent) {
    size_t size = lengths.size();
    if (size == 0) return 0.0f;

    std::sort(lengths.begin(), lengths.end());

    size_t trimSize = static_cast<size_t>(trimPercent * size);
    float sum = 0.0f;
    for (size_t i = trimSize; i < size - trimSize; ++i) {
        sum += lengths[i];
    }

    return sum / (size - 2 * trimSize);
}


float Common::caculateAverageAngle(const std::vector<float>& angles) {
    float sum_x = 0.0;
    float sum_y = 0.0;

    for (float angle : angles) {
        // Convert to radians because std::cos and std::sin expect radians.
        float radian = angle * M_PI / 180.0;
        sum_x += cos(radian);
        sum_y += sin(radian);
    }

    float avg_x = sum_x / angles.size();
    float avg_y = sum_y / angles.size();

    // Calculate the average angle in radians.
    float avg_radian = atan2(avg_y, avg_x);

    // Convert back to degrees.
    float avg_angle = avg_radian * 180.0 / M_PI;

    // Ensure the result is within [0, 360).
    if (avg_angle < 0) avg_angle += 360.0;

    return avg_angle;
}