#include "sensorobjectsviewer.h"

namespace VISUALIZATION{
#ifdef ROS1_FOUND
SensorObjectsViewer::SensorObjectsViewer(ros::NodeHandle& nh, const std::string& pubTopicName, const int& showType) 
	:Viewer<common_msgs::sensorobjects>(nh, pubTopicName, showType)
{
	
}

SensorObjectsViewer::~SensorObjectsViewer() 
{

}

void SensorObjectsViewer::visualize(const common_msgs::sensorobjects& objects){
	std::string frameIDInfo = "car";

	visualization_msgs::MarkerArray objectsMarkerArray;

	visualization_msgs::Marker marker;
	marker.action=visualization_msgs::Marker::DELETEALL;
	objectsMarkerArray.markers.emplace_back(marker);

	for (int i=0; i<objects.obs.size(); i++){
		vector<float> color;
		if(int(objects.obs[i].classification) < class_color.size()){
			color = class_color[int(objects.obs[i].classification)];
		}
		else{
			color = {0.5, 0, 0};//僵尸车
		}

		visualization_msgs::Marker line_list_detect;
		double stamp = objects.timestamp / 1000.0;
		line_list_detect.header.frame_id  = frameIDInfo;
		//line_list_detect.header.stamp = ros::Time::now();
		line_list_detect.header.stamp = ros::Time().fromSec(stamp);//TODO  lidar时间戳 //ros::Time::now()
		line_list_detect.ns = "points_and_lines";
		line_list_detect.lifetime =ros::Duration();//0.1
		line_list_detect.action = visualization_msgs::Marker::ADD;
		line_list_detect.pose.orientation.w = 1.0;
		line_list_detect.id = i;
		line_list_detect.type = visualization_msgs::Marker::LINE_LIST;
		//line width
		line_list_detect.scale.x = 0.3;
		//line_list_detect.scale.y = 0.1;
		//line_list_detect.scale.z = 0.1;
		//color green
		line_list_detect.color.a = 1;//透明度
		line_list_detect.color.r = 1;//color[0]; //检测颜色 淡红色
		line_list_detect.color.g = 0.5;//color[1];
		line_list_detect.color.b = 0.5;//color[2];

		geometry_msgs::Point p0, p1, p2, p3;
		geometry_msgs::Point p4, p5, p6, p7;
		// cout <<"corner size = " << objects.obs[i].points.size() << endl;
		if(objects.obs[i].points.size() == 8){
			p0.x = objects.obs[i].points[0].x;		p0.y = objects.obs[i].points[0].y;		p0.z = objects.obs[i].points[0].z;
			p1.x = objects.obs[i].points[1].x;		p1.y = objects.obs[i].points[1].y;		p1.z = objects.obs[i].points[1].z;
			p2.x = objects.obs[i].points[2].x;		p2.y = objects.obs[i].points[2].y;		p2.z = objects.obs[i].points[2].z;
			p3.x = objects.obs[i].points[3].x;		p3.y = objects.obs[i].points[3].y;		p3.z = objects.obs[i].points[3].z;
			p4.x = objects.obs[i].points[4].x;		p4.y = objects.obs[i].points[4].y;		p4.z = objects.obs[i].points[4].z;
			p5.x = objects.obs[i].points[5].x;		p5.y = objects.obs[i].points[5].y;		p5.z = objects.obs[i].points[5].z;
			p6.x = objects.obs[i].points[6].x;		p6.y = objects.obs[i].points[6].y;		p6.z = objects.obs[i].points[6].z;
			p7.x = objects.obs[i].points[7].x;		p7.y = objects.obs[i].points[7].y;		p7.z = objects.obs[i].points[7].z;
		}
		else{
			float headingClockwise = 2.0 * M_PI - objects.obs[i].azimuth; 
			vector<double> boxInfo = {objects.obs[i].x, objects.obs[i].y, objects.obs[i].z,
		                          objects.obs[i].length, objects.obs[i].width, objects.obs[i].height,
		                          headingClockwise};
			vector<vector<double>> eightCornerPoints = this->m_cCommon.boxes_to_corners_3d(boxInfo);

			p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
			p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
			p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
			p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
			p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
			p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
			p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
			p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
		}
	
		//bottom
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p1);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p2);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p3);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p0);
		//top
		line_list_detect.points.push_back(p4); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p5); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p6); line_list_detect.points.push_back(p7);
		line_list_detect.points.push_back(p7); line_list_detect.points.push_back(p4);
		//side
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p4);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p7);
		//direction
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p7);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p4);

		if(m_showType != ShowType::Radar_MatchedLidar)
			objectsMarkerArray.markers.push_back(line_list_detect);

		visualization_msgs::Marker text;
		text.header.frame_id = frameIDInfo;
		text.header.stamp = ros::Time().fromSec(stamp); //ros::Time::now()
		text.ns = "box";
		text.action = visualization_msgs::Marker::ADD;
		text.lifetime =ros::Duration();//0.1
		text.pose.orientation.w = 1;
		text.pose.position.x = objects.obs[i].x;
		text.pose.position.y = objects.obs[i].y;
		text.pose.position.z = objects.obs[i].z;
		text.id = i;
		text.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
		text.scale.z = 0.3;

		text.color.a = 1;//透明度
		text.color.r = 1;
		text.color.g = 1;
		text.color.b = 1;//字体为白色{1, 1, 1}

		//添加目标横纵向速度信息
		ostringstream str;
		str << std::setiosflags(std::ios::fixed) << std::setprecision(2) << objects.obs[i].relspeedx;
		std::string vxString = str.str();
		str.str("");//清空数据
		str <<  objects.obs[i].relspeedy;
		std::string vyString = str.str();

		//添加跟踪类型
		//20221103 //0.初始目标 1.跟踪 2.lidar 3.lidar-radar-camera 4.融合中lidar-radar -20220908 20230111 简化
		// cout << "debug: value = " << (int)objects.obs[i].value <<  endl;
		int speedSourceValue = (int)objects.obs[i].value;
		std::string speedSource = speedSourceValue < v_speedSourceValue.size() ? v_speedSourceValue[speedSourceValue] : v_speedSourceValue[v_speedSourceValue.size() - 1];

		//添加目标运动状态
		//20220908 20230111 简化
		//std::string motionInfo = msg[i].motionInfo < v_motionInfo.size() ? v_motionInfo[msg[i].motionInfo] : v_motionInfo[v_motionInfo.size() - 1];
		std::string confidence = std::to_string(objects.obs[i].confidence);
		confidence = confidence.substr(0,4);

		string positionX = std::to_string(objects.obs[i].x);
		string positionY = std::to_string(objects.obs[i].y);
		// 显示：id
		// 第几次匹配成功-预测帧数
		// 类别-速度来源-运动信息
		// 横向速度-纵向速度
		// 置信度-驾驶意图-radarIndex-radarObjectID
		if(this->m_showType == ShowType::Radar_MatchedLidar){
			text.text = "Radar_MatchedLidar:\nID:" +std::to_string(objects.obs[i].id)
				            + "\nlabel:" + std::to_string(objects.obs[i].classification) 
							// + "-value:" + std::to_string(objects.obs[i].value)
				            // + "\nX:" + positionX + "\nY:" + positionY
				            // + "\nL:" + std::to_string(objects.obs[i].length) + "\nW:" + std::to_string(objects.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            // + "\nconfidence:" + confidence
				            // + "\nradarIndex:" + std::to_string(objects.obs[i].radarIndex)
				            // + "\nradarObjectID:" + std::to_string(objects.obs[i].radarObjectID)
							;
		}
		else if(this->m_showType == ShowType::FusionTrackedObjects){
			text.text =
				"FusionTrackedObjects:\nX:" + positionX + "\nY:" + positionY
				+ "\nlabel:" + std::to_string(objects.obs[i].classification)
				+ "\nL:" + std::to_string(objects.obs[i].length) + "\nW:" + std::to_string(objects.obs[i].width)
				+ "\nheading:" + std::to_string(objects.obs[i].azimuth * 180.0 / M_PI)
				+ "\nabsVx:" + vxString + "\nabsVy:" + vyString
				+ "\nconfidence:" + confidence;
		}
		else{
			text.text =
				"FusionTrackedObjects:\nX:" + positionX + "\nY:" + positionY
				+ "\nlabel:" + std::to_string(objects.obs[i].classification)
				+ "\nL:" + std::to_string(objects.obs[i].length) + "\nW:" + std::to_string(objects.obs[i].width)
				+ "\nheading:" + std::to_string(objects.obs[i].azimuth * 180.0 / M_PI)
				+ "\nabsVx:" + vxString + "\nabsVy:" + vyString
				+ "\nconfidence:" + confidence;
		}
		objectsMarkerArray.markers.push_back(text);

	}

	this->pub_objectsMarker.publish(objectsMarkerArray);
	objectsMarkerArray.markers.clear();

}
#endif
#ifdef ROS2_FOUND
SensorObjectsViewer::SensorObjectsViewer(rclcpp::Node::SharedPtr& nh, const std::string& pubTopicName, const int& showType)
	:Viewer<common_msgs_humble::msg::Sensorobjects>(nh, pubTopicName, showType)
{
	
}

SensorObjectsViewer::~SensorObjectsViewer() 
{

}

void SensorObjectsViewer::visualize(const common_msgs_humble::msg::Sensorobjects& objects){
	std::string frameIDInfo = "car";

	visualization_msgs::msg::MarkerArray objectsMarkerArray;

	visualization_msgs::msg::Marker marker;
	marker.action=visualization_msgs::msg::Marker::DELETEALL;
	objectsMarkerArray.markers.emplace_back(marker);

	for (int i=0; i<objects.obs.size(); i++){
		vector<float> color;
		if(int(objects.obs[i].classification) < class_color.size()){
			color = class_color[int(objects.obs[i].classification)];
		}
		else{
			color = {0.5, 0, 0};//僵尸车
		}

		visualization_msgs::msg::Marker line_list_detect;
		double stamp = objects.timestamp / 1000.0;
		line_list_detect.header.frame_id  = frameIDInfo;
		line_list_detect.header.stamp = rclcpp::Time(stamp, RCL_ROS_TIME);//getDateTime(stamp);
		line_list_detect.ns = "points_and_lines";
		line_list_detect.lifetime =rclcpp::Duration::from_seconds(1e9);//0.1
		line_list_detect.action = visualization_msgs::msg::Marker::ADD;
		line_list_detect.pose.orientation.w = 1.0;
		line_list_detect.id = i;
		line_list_detect.type = visualization_msgs::msg::Marker::LINE_LIST;
		//line width
		line_list_detect.scale.x = 0.3;
		//line_list_detect.scale.y = 0.1;
		//line_list_detect.scale.z = 0.1;
		//color green
		line_list_detect.color.a = 1;//透明度
		line_list_detect.color.r = 1;//color[0]; //检测颜色 淡红色
		line_list_detect.color.g = 0.5;//color[1];
		line_list_detect.color.b = 0.5;//color[2];

		geometry_msgs::msg::Point p0, p1, p2, p3;
		geometry_msgs::msg::Point p4, p5, p6, p7;
		// cout <<"corner size = " << objects.obs[i].points.size() << endl;
		if(objects.obs[i].points.size() == 8){
			p0.x = objects.obs[i].points[0].x;		p0.y = objects.obs[i].points[0].y;		p0.z = objects.obs[i].points[0].z;
			p1.x = objects.obs[i].points[1].x;		p1.y = objects.obs[i].points[1].y;		p1.z = objects.obs[i].points[1].z;
			p2.x = objects.obs[i].points[2].x;		p2.y = objects.obs[i].points[2].y;		p2.z = objects.obs[i].points[2].z;
			p3.x = objects.obs[i].points[3].x;		p3.y = objects.obs[i].points[3].y;		p3.z = objects.obs[i].points[3].z;
			p4.x = objects.obs[i].points[4].x;		p4.y = objects.obs[i].points[4].y;		p4.z = objects.obs[i].points[4].z;
			p5.x = objects.obs[i].points[5].x;		p5.y = objects.obs[i].points[5].y;		p5.z = objects.obs[i].points[5].z;
			p6.x = objects.obs[i].points[6].x;		p6.y = objects.obs[i].points[6].y;		p6.z = objects.obs[i].points[6].z;
			p7.x = objects.obs[i].points[7].x;		p7.y = objects.obs[i].points[7].y;		p7.z = objects.obs[i].points[7].z;
		}
		else{
			float headingClockwise = 2.0 * M_PI - objects.obs[i].azimuth; 
			vector<double> boxInfo = {objects.obs[i].x, objects.obs[i].y, objects.obs[i].z,
		                          objects.obs[i].length, objects.obs[i].width, objects.obs[i].height,
		                          headingClockwise};
			vector<vector<double>> eightCornerPoints = this->m_cCommon.boxes_to_corners_3d(boxInfo);

			p0.x = eightCornerPoints[0][0];		p0.y = eightCornerPoints[0][1];		p0.z = eightCornerPoints[0][2];
			p1.x = eightCornerPoints[1][0];		p1.y = eightCornerPoints[1][1];		p1.z = eightCornerPoints[1][2];
			p2.x = eightCornerPoints[2][0];		p2.y = eightCornerPoints[2][1];		p2.z = eightCornerPoints[2][2];
			p3.x = eightCornerPoints[3][0];		p3.y = eightCornerPoints[3][1];		p3.z = eightCornerPoints[3][2];
			p4.x = eightCornerPoints[4][0];		p4.y = eightCornerPoints[4][1];		p4.z = eightCornerPoints[4][2];
			p5.x = eightCornerPoints[5][0];		p5.y = eightCornerPoints[5][1];		p5.z = eightCornerPoints[5][2];
			p6.x = eightCornerPoints[6][0];		p6.y = eightCornerPoints[6][1];		p6.z = eightCornerPoints[6][2];
			p7.x = eightCornerPoints[7][0];		p7.y = eightCornerPoints[7][1];		p7.z = eightCornerPoints[7][2];
		}
	
		//bottom
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p1);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p2);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p3);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p0);
		//top
		line_list_detect.points.push_back(p4); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p5); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p6); line_list_detect.points.push_back(p7);
		line_list_detect.points.push_back(p7); line_list_detect.points.push_back(p4);
		//side
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p4);
		line_list_detect.points.push_back(p1); line_list_detect.points.push_back(p5);
		line_list_detect.points.push_back(p2); line_list_detect.points.push_back(p6);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p7);
		//direction
		line_list_detect.points.push_back(p0); line_list_detect.points.push_back(p7);
		line_list_detect.points.push_back(p3); line_list_detect.points.push_back(p4);

		if(m_showType != ShowType::Radar_MatchedLidar)
			objectsMarkerArray.markers.push_back(line_list_detect);

		visualization_msgs::msg::Marker text;
		text.header.frame_id = frameIDInfo;
		text.header.stamp = rclcpp::Time(stamp, RCL_ROS_TIME);; //ros::Time::now()
		text.ns = "box";
		text.action = visualization_msgs::msg::Marker::ADD;
		text.lifetime = rclcpp::Duration::from_seconds(1e9);//0.1
		text.pose.orientation.w = 1;
		text.pose.position.x = objects.obs[i].x;
		text.pose.position.y = objects.obs[i].y;
		text.pose.position.z = objects.obs[i].z;
		text.id = i;
		text.type = visualization_msgs::msg::Marker::TEXT_VIEW_FACING;
		text.scale.z = 0.3;

		text.color.a = 1;//透明度
		text.color.r = 1;
		text.color.g = 1;
		text.color.b = 1;//字体为白色{1, 1, 1}

		//添加目标横纵向速度信息
		ostringstream str;
		str << std::setiosflags(std::ios::fixed) << std::setprecision(2) << objects.obs[i].relspeedx;
		std::string vxString = str.str();
		str.str("");//清空数据
		str <<  objects.obs[i].relspeedy;
		std::string vyString = str.str();

		//添加跟踪类型
		//20221103 //0.初始目标 1.跟踪 2.lidar 3.lidar-radar-camera 4.融合中lidar-radar -20220908 20230111 简化
		// cout << "debug: value = " << (int)objects.obs[i].value <<  endl;
		int speedSourceValue = (int)objects.obs[i].value;
		std::string speedSource = speedSourceValue < v_speedSourceValue.size() ? v_speedSourceValue[speedSourceValue] : v_speedSourceValue[v_speedSourceValue.size() - 1];

		//添加目标运动状态
		//20220908 20230111 简化
		//std::string motionInfo = msg[i].motionInfo < v_motionInfo.size() ? v_motionInfo[msg[i].motionInfo] : v_motionInfo[v_motionInfo.size() - 1];
		std::string confidence = std::to_string(objects.obs[i].confidence);
		confidence = confidence.substr(0,4);

		string positionX = std::to_string(objects.obs[i].x);
		string positionY = std::to_string(objects.obs[i].y);
		// 显示：id
		// 第几次匹配成功-预测帧数
		// 类别-速度来源-运动信息
		// 横向速度-纵向速度
		// 置信度-驾驶意图-radarIndex-.radarobjectID
		if(this->m_showType == ShowType::Radar_MatchedLidar){
			text.text = "Radar_MatchedLidar:\nID:" +std::to_string(objects.obs[i].id)
				            + "\nlabel:" + std::to_string(objects.obs[i].classification) 
							// + "-value:" + std::to_string(objects.obs[i].value)
				            // + "\nX:" + positionX + "\nY:" + positionY
				            // + "\nL:" + std::to_string(objects.obs[i].length) + "\nW:" + std::to_string(objects.obs[i].width)
				            + "\nabsVx:" + vxString + "\nabsVy:" + vyString
				            // + "\nconfidence:" + confidence
				            // + "\nradarIndex:" + std::to_string(objects.obs[i].radarindex)
				            // + "\nradarObjectID:" + std::to_string(objects.obs[i]..radarobjectID)
							;
		}
		else if(this->m_showType == ShowType::FusionTrackedObjects){
			text.text =
				"FusionTrackedObjects:\nX:" + positionX + "\nY:" + positionY
				+ "\nlabel:" + std::to_string(objects.obs[i].classification)
				+ "\nL:" + std::to_string(objects.obs[i].length) + "\nW:" + std::to_string(objects.obs[i].width)
				+ "\nheading:" + std::to_string(objects.obs[i].azimuth * 180.0 / M_PI)
				+ "\nabsVx:" + vxString + "\nabsVy:" + vyString
				+ "\nconfidence:" + confidence;
		}
		else{
			text.text =
				"FusionTrackedObjects:\nX:" + positionX + "\nY:" + positionY
				+ "\nlabel:" + std::to_string(objects.obs[i].classification)
				+ "\nL:" + std::to_string(objects.obs[i].length) + "\nW:" + std::to_string(objects.obs[i].width)
				+ "\nheading:" + std::to_string(objects.obs[i].azimuth * 180.0 / M_PI)
				+ "\nabsVx:" + vxString + "\nabsVy:" + vyString
				+ "\nconfidence:" + confidence;
		}
		objectsMarkerArray.markers.push_back(text);

	}

	pub_objectsMarker->publish(objectsMarkerArray);
	objectsMarkerArray.markers.clear();

}
#endif
} // namespace VISUALIZATION