/********************************************************************************
* @author: shuang<PERSON>n han
* @date: 2022/7/4 上午9:43
* @version: 1.0
* @description: 
********************************************************************************/


#ifndef SRC_COMPUTEASSIGNMENTMATRIX_H
#define SRC_COMPUTEASSIGNMENTMATRIX_H
#include <iostream>
#include <vector>
#include <eigen3/Eigen/Core>
#include <eigen3/Eigen/Dense>

using namespace std;

	class ComputeAssignmentMatrix{
	private:
		vector<float> predictBox_;
		vector<float> detectedBox_;
		vector<float> distanceRatio_;//关联特征的比例
		float meaningDelta_;//关联值有用的最小数据
		
		float boxCenterDistance_;
		float boxCenterDistanceBetweenTwoFrame_;
		float ellipseLocationDistance_; //20221014 椭圆门限距离
		float directionDistance_;
		float boxSizeDistance_;
		float pointNumDistance_;
		float histogramDistance_;
		float CentroidShiftDistance_;
		float boxIOUDistance_;
		
		float weightingDistance_;//所有关联特征的加权
		std::vector<float> matchParams_;
		
		
	
	public:
		vector<vector<double>> m_assignmentMatrix;
		vector<int> m_assignmentPairIndex;

		ComputeAssignmentMatrix();
		~ComputeAssignmentMatrix(){};
		
		void setAssociateObject(const vector<float>& predictBox, const vector<float>& detectedBox);
		void setAssignmentMatrix(const int& firstObjectsSize, const int& secondObjectsSize);
		void setPredictedObject(const vector<float>& predictBox);
		void setDetectedObject(const vector<float>& detectedBox);
		void setMatchParams(const std::vector<float>& matchParams);
		
		template<typename T>
		T calculateCosTheta2DXY(const Eigen::Matrix<T,3,1>& v1, const Eigen::Matrix<T,3,1>& v2)
		{
			T v1_len = static_cast<T>(sqrt((v1.head(2).cwiseProduct(v1.head(2))).sum()));
			T v2_len = static_cast<T>(sqrt((v2.head(2).cwiseProduct(v2.head(2))).sum()));
			if(v1_len < std::numeric_limits<T>::epsilon() ||
			   v2_len < std::numeric_limits<T>::epsilon()){
				return 0.0;
			}
			//点积的计算公式:角度越小，值越大
			T cos_theta = (v1.head(2).cwiseProduct(v2.head(2))).sum() / (v1_len * v2_len);
			T cosDistance = T(1.0) - cos_theta;
			//std::cout<<"cosDistance: "<<cosDistance<<std::endl;
			return cosDistance;
		}
		
		/// 计算相应关联值
		void locationDistance(const vector<float>& predictBox, const vector<float>& detectedBox,bool useApollo);
		void ellipseLocationDistance();//20221014 椭圆门限距离
		void directionDistance(const vector<float>& predictBox, const vector<float>& detectedBox);
		void boxSizeDistance(const vector<float>& predictBox,const vector<float>& detectedBox);
		void boxIOUDistance(const vector<float>& predictBox,const vector<float>& detectedBox);
		
		void runComputeAssigenmentMatrix();
		float addDistanceWeighting();
		
		float run();
		
		/// 获取对应关联项的关联值
		float getLocationDistance();
		float getEllipseLocationDistance();//20221014 椭圆门限距离
		float getDirectionDistance();
		float getBoxSizeDistance();
		float getBoxIOUDistance();
		float getWeightingDistance();
		
		///重置参数
		void resetDistance();
		
	};
	

#endif //SRC_COMPUTEASSIGNMENTMATRIX_H
