/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-06-26 11:00:53
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-09-04 15:56:29
 * @FilePath: /autodrivingVersionTest/src/perception/commonlibrary/src/logger/logger.h
 * Copyright 2024 vanjee, All Rights Reserved. 
 * 2024-06-26 11:00:53
 */
#ifndef _LOGGER_H_
#define _LOGGER_H_
#include <iostream>
#include <vector>
#include <boost/make_shared.hpp>
#include <time.h>
#include <sys/stat.h>
#include "spdlog/spdlog.h"
#include "spdlog/async.h"
#include "spdlog/sinks/basic_file_sink.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/sinks/rotating_file_sink.h"
#include <limits.h>
#include "../configManager/configManager.h"


using namespace std;

#define LOG_TRACE(msg, ...) logger->trace(suffix(msg), ##__VA_ARGS__)
#define LOG_DEBUG(msg, ...) logger->debug(suffix(msg), ##__VA_ARGS__)
#define LOG_INFO(msg, ...) logger->info(suffix(msg), ##__VA_ARGS__)
#define LOG_WARN(msg, ...) logger->warn(suffix(msg), ##__VA_ARGS__)
#define LOG_ERROR(msg, ...) logger->error(suffix(msg), ##__VA_ARGS__)
#define LOG_FATAL(msg, ...) logger->critical(suffix(msg), ##__VA_ARGS__)


// use embedded macro to support file and line number
// #define UVLOG_TRACE(...)  SPDLOG_LOGGER_CALL(CLogger::getInstance()->getLogger().get(), spdlog::level::trace, __VA_ARGS__)
// #define UVLOG_DEBUG(...)  SPDLOG_LOGGER_CALL(CLogger::getInstance()->getLogger().get(), spdlog::level::debug, __VA_ARGS__)
// #define UVLOG_INFO(...)   SPDLOG_LOGGER_CALL(CLogger::getInstance()->getLogger().get(), spdlog::level::info, __VA_ARGS__)
// #define UVLOG_WARN(...)   SPDLOG_LOGGER_CALL(CLogger::getInstance()->getLogger().get(), spdlog::level::warn, __VA_ARGS__)
// #define UVLOG_ERROR(...)  SPDLOG_LOGGER_CALL(CLogger::getInstance()->getLogger().get(), spdlog::level::err, __VA_ARGS__)
 
/***********************************************************************************
//格式化的例子：
	UVLOG_INFO("print data,{},{}", 1000, "hello!");
	UVLOG_INFO("Hello, {}!", "World");
	UVLOG_INFO("Welcome to spdlog!");
	UVLOG_ERROR("Some error message with arg: {}", 1);
	UVLOG_WARN("Easy padding in numbers like {:08d}", 12);
	UVLOG_INFO("Support for int: {0:d};  hex: {0:x};  oct: {0:o}; bin: {0:b}", 42);
	UVLOG_INFO("Support for floats {:03.2f}", 1.23456);
	UVLOG_INFO("Positional args are {1} {0}..", "too", "supported");
	UVLOG_INFO("{:<30}", "left aligned");
**********************************************************************************/

class Logger{
public:
    Logger(const boost::shared_ptr<ConfigManager::ConfigManager>& configManager);
    ~Logger();
    void InitLogger();
    void UninitLogger();
    void loggerSetting();
    std::shared_ptr<spdlog::logger>& getLogger();
    char* createDirectoryWithDateAndGetPath(const char* baseDir);
    std::string  getCurrentTimeString();

public:
    enum OutputType{
        Terminal = 0,
        File = 1,
        Both = 2
    };

private:
    std::shared_ptr<spdlog::logger> m_pLogger;
    boost::shared_ptr<ConfigManager::ConfigManager> m_pConfigManager;
};

#endif