//
// Created by wkx on 2022/4/20.
//
#include <iostream>
#include <math.h>
#include <vector>
#include <opencv2/core/core.hpp>
#include <eigen3/Eigen/Dense>
#include "getiou.h"
#include <opencv2/imgproc.hpp>
#include <limits.h>

using namespace std;

///////////////////////////////////////////////////////////////////////////////////////////
//opencv get iou RotatedRect rRect = RotatedRect(Point2f(100,100), Size2f(100,50), 30);
//设置一个旋转矩形3个参数分别为：质心（矩形中心），矩形长宽100、50 旋转角度：30 （clockwise）
//RotatedRect 函数返回一个旋转矩形对象
float calcIOU(cv::RotatedRect rect1, cv::RotatedRect rect2)
{
	if(1){//方法1
		// Convert the rotated rectangles to convex polygons
		std::vector<cv::Point2f> poly1(4), poly2(4);
		rect1.points(&poly1[0]);
		rect2.points(&poly2[0]);
		// Compute the intersection of the convex polygons
		std::vector<cv::Point2f> intersection;
		cv::intersectConvexConvex(poly1, poly2, intersection);
		
		// Compute the area of the intersection
		double intersectionArea = 0;
		if (intersection.size() >= 3) {
			intersectionArea = cv::contourArea(intersection);
		}
		
		// Compute the area of the union
		double unionArea = cv::contourArea(poly1) + cv::contourArea(poly2) - intersectionArea;
		if (unionArea <= 0) {
			return 0;  // 避免分母为0
		}
		
		// Compute the IoU
		double iou = intersectionArea / unionArea;
		return iou;
		// Print the IoU
		std::cout << "IoU: " << iou << std::endl;
	}
	else{//方法2-个别帧报错
		float areaRect1 = rect1.size.width * rect1.size.height;
		float areaRect2 = rect2.size.width * rect2.size.height;
		vector<cv::Point2f> vertices;//存放8角点
		//计算两个旋转矩形框的相交多边形，返回的结果vertices是多边形的点坐标，最多返回8个点的坐标。
		int intersectionType = cv::rotatedRectangleIntersection(rect1, rect2, vertices);
		if (vertices.size()==0){
			cout<<"INFO: 相交多边形定点为0\n";
			return 0.0;
		}
		else{
			cout<<"INFO: 相交多边形定点个数："<<vertices.size()<< endl;
			vector<cv::Point2f> order_pts;
			// 找到交集（交集的区域），对轮廓的各个点进行排序
			//计算出图像的凸包，根据图像的轮廓点，通过函数convexhull转化成凸包的点点坐标，从而画出图像的凸包。
			//InputArray points: 得到的点集，一般是用图像轮廓函数求得的轮廓点
			//OutputArray hull: 输出的是凸包的二维xy点的坐标值，针对每一个轮廓形成的
			//bool clockwise = false: 表示凸包的方向，顺时针或者逆时针
			//bool returnPoint = true: 表示返回点还是点地址的索引
			cv::convexHull(cv::Mat(vertices), order_pts, true);
			//计算多边形面积，这样就得到了相交多边形的面积。
			double area = cv::contourArea(order_pts);
			//交并比
			float inner = (float) (area / (areaRect1 + areaRect2 - area + 0.0001));
			return inner;
		}
	}
	
	
}
///////////////////////////////////////////////////////////////////////////////////////////
//opencv get iou RotatedRect rRect = RotatedRect(Point2f(100,100), Size2f(100,50), 30);
//设置一个旋转矩形3个参数分别为：质心（矩形中心），矩形长宽100、50 旋转角度：30 （clockwise）
//RotatedRect 函数返回一个旋转矩形对象


//https://blog.csdn.net/zjn295771349/article/details/112989543#:~:text=cv%3A%3ArotatedRectangleIntersection%20%28cv%3A%3ARotatedRect%20r1%EF%BC%8C%20cv%3A%3ARotatedRect,r1%EF%BC%8C%20vector%3Ccv%3A%3APoint2f%3E%20vertices%29%E7%94%A8%E6%9D%A5%E8%AE%A1%E7%AE%97%E4%B8%A4%E4%B8%AA%20%E6%97%8B%E8%BD%AC%E7%9F%A9%E5%BD%A2%E6%A1%86%20%E7%9A%84%E7%9B%B8%E4%BA%A4%E5%A4%9A%E8%BE%B9%E5%BD%A2%EF%BC%8C%E8%BF%94%E5%9B%9E%E7%9A%84%E7%BB%93%E6%9E%9Cvertices%E6%98%AF%E5%A4%9A%E8%BE%B9%E5%BD%A2%E7%9A%84%E7%82%B9%E5%9D%90%E6%A0%87%EF%BC%8C%E6%9C%80%E5%A4%9A%E8%BF%94%E5%9B%9E8%E4%B8%AA%E7%82%B9%E7%9A%84%E5%9D%90%E6%A0%87%E3%80%82
/////////////////////////////////////////////////////////////////////////////////
//解决rotatedRectangleIntersection计算目标检测旋转框IOU不准确问题C++、opencv
typedef struct{
    float x;
    float y;
    float w;
    float h;
    float theta;
    float score;
    int label;
}detection;

// 第一步找到最左边的点
int find_leftmost_point(vector<cv::Point2f> intersectingRegion)
{
    int index = 0;
    float tmp = intersectingRegion[0].x;
    for(int i=1; i<intersectingRegion.size(); i++)
    {
        if(intersectingRegion[i].x < tmp)
        {
            tmp = intersectingRegion[i].x;
            index = i;
        }
    }
    return index;
}

//第二步对所有点进行排序
vector<cv::Point2f> sort_points(vector<cv::Point2f> intersectingRegion)
{
    vector<cv::Point2f> sort_intersectingRegion;
    int leftmost_index = find_leftmost_point(intersectingRegion);

    vector<float> arctan;
    for(int i=0; i<intersectingRegion.size(); i++)
    {
        arctan.push_back(atan2(intersectingRegion[i].y - intersectingRegion[leftmost_index].y, intersectingRegion[i].x - intersectingRegion[leftmost_index].x));
    }

    vector<int> index;
    for(int i=0; i<arctan.size(); i++)
    {
        index.push_back(i);
    }

    sort(index.begin(), index.end(), [&](const int& a, const int& b) {return (arctan[a] > arctan[b]);});

    for(int i=0; i<index.size(); i++)
    {
        sort_intersectingRegion.push_back(intersectingRegion[index[i]]);
    }
    return std::move(sort_intersectingRegion);
}

// 计算两个旋转框的IOU
float rbox_iou(detection d1, detection d2)
{
    float inter_area;
    float area_r1 = d1.w * d1.h;
    float area_r2 = d2.w * d2.h;
    cv::RotatedRect rect1;
    rect1.center = cv::Point2f(d1.x, d1.y);
    rect1.size = cv::Size(d1.w, d1.h);
    rect1.angle = d1.theta;
    cv::RotatedRect rect2;
    rect2.center = cv::Point2f(d2.x, d2.y);
    rect2.size = cv::Size(d2.w, d2.h);
    rect2.angle = d2.theta;

    vector<cv::Point2f> intersectingRegion;
    cv::rotatedRectangleIntersection(rect1, rect2, intersectingRegion);

    if (intersectingRegion.empty())
    {
        inter_area = 0;
    }
    else
    {
        vector<cv::Point2f> sort_intersectingRegion = sort_points(intersectingRegion);
        inter_area = cv::contourArea(sort_intersectingRegion);
    }
    return inter_area / (area_r1 + area_r2 - inter_area + 0.00000001);
}
/////////////////////////////////////////////////////////////////////////////////
//解决rotatedRectangleIntersection计算目标检测旋转框IOU不准确问题C++、opencv




bool bInBox(const vector<cv::Point2f> &vpBoxA, const cv::Point2f &p)
{
    std::vector<cv::Point2f> corners = vpBoxA;
    for(int i = 0; i<vpBoxA.size(); i++)   //01230123
        corners.push_back(vpBoxA[i]);

    std::vector< std::vector<double> > linesA;
    for(int i = 0; i<vpBoxA.size(); i++)
    {
        cv::Point2f p1 = corners[i];
        cv::Point2f p2 = corners[i+1];
        cv::Point2f p3 = corners[i+2];
        double a;
        if(p1.x - p2.x == 0)
            a = -(p1.y - p2.y)/0.0001;
        else
            a = -(p1.y - p2.y)/(p1.x - p2.x);

        double b = 1;
        double c = -a * p2.x - p2.y;
        double d = a*p3.x + b*p3.y + c;

        std::vector<double> line{a,b,c,d};
        linesA.push_back(line);
    }

    for(int i=0; i<linesA.size(); i++)
    {
        vector<double > l = linesA[i];
        double y = l[0] * p.x + l[1] * p.y +l[2];
        if(y * l[3] < 0)
            return false;
    }
    return true;
}

double InterSection_2D(const vector<cv::Point2f> &vpBoxA, const vector<cv::Point2f> &vpBoxB)
{
    double min_x, max_x, min_y, max_y;
    min_x = vpBoxA[0].x;
    max_x = vpBoxA[0].x;
    min_y = vpBoxA[0].y;
    max_y = vpBoxA[0].y;

    for(int i = 1; i<vpBoxA.size(); i++)
    {
        cv::Point2f p = vpBoxA[i];
        if(p.x > max_x)
            max_x = p.x;
        if(p.x < min_x)
            min_x = p.x;
        if(p.y > max_y)
            max_y = p.y;
        if(p.y < min_y)
            min_y = p.y;
    }
    for(int i=0; i<vpBoxB.size(); i++)
    {
        cv::Point2f p = vpBoxB[i];
        if(p.x > max_x)
            max_x = p.x;
        if(p.x < min_x)
            min_x = p.x;
        if(p.y > max_y)
            max_y = p.y;
        if(p.y < min_y)
            min_y = p.y;
    }

    //将两个BBox的定点坐标最小值设置为0, 以防止有负数的产生
    vector<cv::Point2f> vpBoxAA = vpBoxA;
    vector<cv::Point2f> vpBoxBB = vpBoxB;

    for(int i=0; i<vpBoxA.size(); i++)
    {
        vpBoxAA[i].x = vpBoxAA[i].x - min_x;
        vpBoxAA[i].y = vpBoxAA[i].y - min_y;
        vpBoxBB[i].x = vpBoxBB[i].x - min_x;
        vpBoxBB[i].y = vpBoxBB[i].y - min_y;
    }

    int imax_x = (int)((max_x - min_x) * 10000);
    int imax_y = (int)((max_y - min_y) * 10000);

    double points_inA = 0, points_inB = 0, points_inAB = 0;
    srand((int)time(0));
    for(int i = 0; i<1000; i++)
    {
        int xx = rand()%(imax_x)+1;  //生成[1, imax_x]之间的整数
        int yy = rand()%(imax_y)+1;  //生成[1, imax_y]之间的整数

        cv::Point2f p((float)xx / 1000.0, (float)yy / 1000.0);
        if( bInBox(vpBoxAA, p) )
            ++points_inA;
        if( bInBox(vpBoxBB, p) )
            ++points_inB;
        if( bInBox(vpBoxAA, p) && bInBox(vpBoxBB, p) )
            ++points_inAB;
    }

    double iou = points_inAB / (points_inA + points_inB - points_inAB);
    return iou;
}

//计算3d-iou
float CuboidIoU(const vector<float>& predictBox,const vector<float>& detectedBox)
{
	detection d1;
	detection d2;
	d1.x = predictBox[0];
	d1.y = predictBox[1];
	d1.w = predictBox[4];
	d1.h = predictBox[3];//length
	d1.theta = predictBox[6] / 3.1415926 * 180.0;
	
	d2.x = detectedBox[0];
	d2.y = detectedBox[1];
	d2.w = detectedBox[4];
	d2.h = detectedBox[3];//length
	d2.theta = detectedBox[6] / 3.1415926 * 180.0;
	
	cv::RotatedRect rect1;
	cv::RotatedRect rect2;
	rect1.center = cv::Point2f(d1.x, d1.y);
	rect1.size = cv::Point2f(d1.w, d1.h);
	rect1.angle = d1.theta;
	rect2.center = cv::Point2f(d2.x, d2.y);
	rect2.size = cv::Point2f(d2.w, d2.h);
	rect2.angle = d2.theta;
	
	float iou_2d = calcIOU(rect1, rect2);//use opencv compute iou 得到交并比IOU
	//cout<<"debug: iou_2d = "<<iou_2d<<endl;
	float iou_3d = 0;
	if (iou_2d > 0) {
		//cout<<"debug: iou_2d = "<<iou_2d<<endl;
		//cout<<"-------------predictBox object13: size = " << predictBox.size()
		//    <<", x = " << predictBox[0]<<", y = " << predictBox[1]<<", z = " << predictBox[2]
		//    <<", length = " << predictBox[3]<<", width = " << predictBox[4]<<", heigh = " << predictBox[5]
		//    <<", azimuth = " <<predictBox[6] * 180 / M_PI
		//    << endl;
		//cout<<"-------------detectedBox object14: size = " << detectedBox.size()
		//    <<", x = " << detectedBox[0]<<", y = " << detectedBox[1]<<", z = " << detectedBox[2]
		//    <<", length = " << detectedBox[3]<<", width = " << detectedBox[4]<<", heigh = " << detectedBox[5]
		//    <<", azimuth = " <<detectedBox[6] * 180 / M_PI
		//    << endl;
		float tru_minz = predictBox[2] - predictBox[5];//0 高暂定为1 TODO  使用高 z和高暂定为0 检测Z值未给出
		float tru_maxz = predictBox[2] + predictBox[5];//2
		
		float land_minz = detectedBox[2] - detectedBox[5];//0
		float land_maxz = detectedBox[2] + detectedBox[5];//2
		
		if (land_maxz <= tru_maxz && land_maxz > tru_minz && tru_maxz != land_minz) {
			float height_iou = (land_maxz - tru_minz) / (tru_maxz - land_minz);
			iou_3d = iou_2d * height_iou;//底面积*高
		} else if (tru_maxz < land_maxz && tru_maxz > land_minz && land_maxz != tru_minz) {
			float height_iou = (tru_maxz - land_minz) / (land_maxz - tru_minz);
			iou_3d = iou_2d * height_iou;
		}
	}
	//if (iou_3d > 0)
	//	cout<<"-------------debug: CuboidIoUiou_3d = "<<iou_3d<<endl;

	return iou_3d;
}

//float CuboidIoU(const Eigen::MatrixXd &truth_poses, const Eigen::MatrixXd &landmark_poses)
//{
//
////    std::vector<cv::Point2f>  vground_points;
////    std::vector<cv::Point2f>  vlandmark_points;
////    if(1)  //通过坐标旋转求取groundtruth立方体中 2D-Boundbox四个顶点的坐标
////    {
////        double cen_x = truth_poses(0,0);
////        double cen_y = truth_poses(0,1);
////        double len = truth_poses(0,6);
////        double wid = truth_poses(0,7);
////        double yaw = truth_poses(0,5);
////
////        double x, y, xx, yy;
////        x = cen_x - len;
////        y = cen_y - wid;
////        xx = (x - cen_x)*cos(yaw) - (y - cen_y)*sin(yaw) + cen_x;
////        yy = (x - cen_x)*sin(yaw) + (y - cen_y)*cos(yaw) + cen_y;
////        cv::Point2f p0(xx, yy);
////
////        x = cen_x - len;
////        y = cen_y + wid;
////        xx = (x - cen_x)*cos(yaw) - (y - cen_y)*sin(yaw) + cen_x;
////        yy = (x - cen_x)*sin(yaw) + (y - cen_y)*cos(yaw) + cen_y;
////        cv::Point2f p1(xx, yy);
////
////        x = cen_x + len;
////        y = cen_y + wid;
////        xx = (x - cen_x)*cos(yaw) - (y - cen_y)*sin(yaw) + cen_x;
////        yy = (x - cen_x)*sin(yaw) + (y - cen_y)*cos(yaw) + cen_y;
////        cv::Point2f p2(xx, yy);
////
////        x = cen_x + len;
////        y = cen_y - wid;
////        xx = (x - cen_x)*cos(yaw) - (y - cen_y)*sin(yaw) + cen_x;
////        yy = (x - cen_x)*sin(yaw) + (y - cen_y)*cos(yaw) + cen_y;
////        cv::Point2f p3(xx, yy);
////
////        vground_points = {p0, p1, p2, p3};
////    }
////
////    if(1)//通过坐标旋转求取landmark中 2D-Boundbox四个顶点的坐标
////    {
////        double cen_x = landmark_poses(0,0);
////        double cen_y = landmark_poses(0,1);
////        double len = landmark_poses(0,6);
////        double wid = landmark_poses(0,7);
////        double yaw = landmark_poses(0,5);
////
////        double x, y, xx, yy;
////        x = cen_x - len;
////        y = cen_y - wid;
////        xx = (x - cen_x)*cos(yaw) - (y - cen_y)*sin(yaw) + cen_x;
////        yy = (x - cen_x)*sin(yaw) + (y - cen_y)*cos(yaw) + cen_y;
////        cv::Point2f p0(xx, yy);
////
////        x = cen_x - len;
////        y = cen_y + wid;
////        xx = (x - cen_x)*cos(yaw) - (y - cen_y)*sin(yaw) + cen_x;
////        yy = (x - cen_x)*sin(yaw) + (y - cen_y)*cos(yaw) + cen_y;
////        cv::Point2f p1(xx, yy);
////
////        x = cen_x + len;
////        y = cen_y + wid;
////        xx = (x - cen_x)*cos(yaw) - (y - cen_y)*sin(yaw) + cen_x;
////        yy = (x - cen_x)*sin(yaw) + (y - cen_y)*cos(yaw) + cen_y;
////        cv::Point2f p2(xx, yy);
////
////        x = cen_x + len;
////        y = cen_y - wid;
////        xx = (x - cen_x)*cos(yaw) - (y - cen_y)*sin(yaw) + cen_x;
////        yy = (x - cen_x)*sin(yaw) + (y - cen_y)*cos(yaw) + cen_y;
////        cv::Point2f p3(xx, yy);
////
////        vlandmark_points = {p0, p1, p2, p3};
////    }
////
//    //other ways for 2-d iou
//    detection d1;
//    detection d2;
//    d1.x = truth_poses(0,0);
//    d1.y = truth_poses(0,1);
//    d1.w = truth_poses(0,6);
//    d1.h = truth_poses(0,7);
//    d1.theta = truth_poses(0,5) / 3.1415926 * 180.0;
//
//    d2.x = landmark_poses(0,0);
//    d2.y = landmark_poses(0,1);
//    d2.w = landmark_poses(0,6);
//    d2.h = landmark_poses(0,7);
//    d2.theta = landmark_poses(0,5) / 3.1415926 * 180.0;
//
////    if (d1.x < 0 || d1.y < 0 || d2.x < 0 || d2.y < 0 || d1.w < 0 || d1.h < 0 || d2.w < 0 || d2.h < 0)
////    {
////        cout<<"debug: " << d1.x <<" "<< d1.y <<" "<< d2.x <<" "<<d2.y<< " "<<d1.w<< " "<<d1.h<<" "<< d2.w<<" "<<d2.h <<endl;
////    }
//
//    cv::RotatedRect rect1;
//    cv::RotatedRect rect2;
//    rect1.center = cv::Point2f(d1.x, d1.y);
//    rect1.size = cv::Point2f(d1.w, d1.h);
//    rect1.angle = d1.theta;
//    rect2.center = cv::Point2f(d2.x, d2.y);
//    rect2.size = cv::Point2f(d2.w, d2.h);
//    rect2.angle = d2.theta;
//
//    float iou_2d = calcIOU(rect1, rect2);//use opencv compute iou 得到交并比IOU
//
//    //if(iou_2d < 0)
//    //{
//    //    cout<<"iou_2d = "<<iou_2d<<endl;
//    //}
//
////    float iou_2d = rbox_iou(d1, d2);
////    float iou_2d = InterSection_2D(vlandmark_points, vground_points);
//    float iou_3d = 0;
//    if (iou_2d >= 0) {
//        float tru_minz = truth_poses(0, 2) - truth_poses(0, 8);//0
//        float tru_maxz = truth_poses(0, 2) + truth_poses(0, 8);//2
//
//        float land_minz = landmark_poses(0, 2) - landmark_poses(0, 8);//0
//        float land_maxz = landmark_poses(0, 2) + landmark_poses(0, 8);//2
//
//        if (land_maxz <= tru_maxz && land_maxz > tru_minz && tru_maxz != land_minz) {
//            float height_iou = (land_maxz - tru_minz) / (tru_maxz - land_minz);
//            iou_3d = iou_2d * height_iou;//底面积*高
//        } else if (tru_maxz < land_maxz && tru_maxz > land_minz && land_maxz != tru_minz) {
//            float height_iou = (tru_maxz - land_minz) / (land_maxz - tru_minz);
//            iou_3d = iou_2d * height_iou;
//        }
//    }
//    //if (abs(iou_3d) > 1)
//    //{
//        cout<<"debug: CuboidIoUiou_3d = "<<iou_3d<<endl;
//    //}
//    return iou_3d;
//}

#include<iostream>
#include<vector>
#include<algorithm>
#include <cmath>


// 解决IOU LOSS在回归的性能损失-IOU的迭代
// input:BOX,GT_BOX,select IOU/GIOU/DIou/CIou
// output:IOU's AREA
double IoU_compute(Box &a,Box &b,bool GIoU,bool DIoU, bool CIoU,bool EIoU,double eps){
	
	double iou=0;
	//这里的原则就是算交集面积，两个BOX右角点最小的点-两个BOX左角点最大的点 就是交集的宽W，同理H也这么算
	double inter_width_line=std::min(a.x1,b.x1)-std::max(a.x0,b.x0);
	inter_width_line=std::max(inter_width_line,0.000);
	double inter_high_line =std::min(a.y1,b.y1)-std::max(a.y0,b.y0);
	inter_high_line=std::max(inter_high_line,0.000);
	//本来原始的判断两个BOX是否有交集，因为上一步计算的交集矩形宽和高如果小于0，证明两个BOX无任何交集，eps是给分母加一个很小的数
	//但是其实没必要，因为本来就是0，并且别GIOU算法是0也需要计算LOSS
	//if(inter_width_line or inter_high_line){
	//得到BOX之间交集和并集
	double inter_area=inter_width_line*inter_high_line;
	double union_area=(a.x1-a.x0)*(a.y1-a.y0)+(b.x1-b.x0)*(b.y1-b.y0)-inter_area+eps;
	iou=inter_area/union_area;
	//std::cout<<"iou:"<<iou<<std::endl;
	//}
	//建立在IOU上引申的三种IOU算法
	if(GIoU or DIoU or CIoU or EIoU){
		//计算最小外接矩形的宽和高，这是三个IOU都需要计算的，其实它们属于衍生关系GIOU->DIOU->CIoU
		//GIoU具有一下特性： 1.与IoU一样，具有非负性、尺度不变性等特性 2.任意B、G都存在，GIoU<=IoU 3.-1< GIoU <=1, 当IoU等于1时，GIoU也等于1 由此可见，只有当B与G重合时，GIoU Loss才会为0，相比IoU Loss，GIoU Loss在任意情况下都可以进行训练
		double smallest_w=std::max(a.x1,b.x1)-std::min(a.x0,b.x0);
		double smallest_h=std::max(a.y1,b.y1)-std::min(a.y0,b.y0);
		if(CIoU or DIoU or EIoU){
			//根据公式，计算两个BOX最小外接矩形的对角线长度
			double c2=pow(smallest_w,2)+pow(smallest_h,2)+eps;  //对角线长度平方为C2
			//计算中心点距离,中心点坐标：(x0+x1)/2 ，（y0+y1)/2,作欧氏距离,得到平方值dis_center
			double dis_center=(pow(b.x0+b.x1-a.x0-a.x1,2)+pow(b.y0+b.y1-a.y0-a.y1,2))/4;
			if(DIoU)
				//相比GIoU，DIoU限制的不是最小外接矩与B与G并集面积的差值，而是直接限制了最小外接矩的面积和B与G中心点的位置，这会使得网络更倾向于移动bounding box的位置来减少Loss。同时也加入了IoU元素来使bounding box与ground truth的覆盖面积更加接近
				return iou-dis_center/c2;
			else if(CIoU ){
				//DIoU只考虑了覆盖面积和中心点距离，所以CIOU又在DIoU的基础上加入了长宽比的因素
				//1.按照公式，需要就算两个BOX的长宽比v
				double v=(4 /pow(M_PI,2)) * pow(atan((b.x1-b.x0) / (b.y1-b.y0)) - atan((a.x1-a.x0) / (a.y1-a.y0)), 2);
				//2.计算权重alpha loss阶段
				double alpha=v/(1+eps-iou+v);
				return iou-(dis_center/c2+v*alpha);
			}
			else{//EIoU ,在DIOU基础上-宽和高的损失 去替代IOU的是相对比，
				double w_dis=pow(a.x1-a.x0-b.x1+b.x0,2);//目标a宽减小幅度与目标b宽减小幅度差
				//std::cout<<"w dis:"<<w_dis<<std::endl;
				double h_dis=pow(a.y1-a.y0-b.y1+b.y0,2);
				double cw2=pow(smallest_w,2)+eps;
				double ch2=pow(smallest_h,2)+eps;
				//std::cout<<"宽比;"<<w_dis/cw2<<std::endl;
				//std::cout<<"高比:"<<h_dis/ch2<<std::endl;
				//std::cout<<"中心点距离比："<<dis_center/c2<<std::endl;
				return iou-(dis_center/c2+w_dis/cw2+h_dis/ch2);
			}
		}
		else{
			//GIOU:解决IoU Loss中当B与G不相交时，Loss为0的问题，保证在没有相交时也会有损失函数值，能够进行反向传播）
			//计算其最小邻接矩形面积convex area
			double convex_area=smallest_w*smallest_h+eps;
			return iou-(convex_area-union_area)/convex_area;
		}
	}
	else
		return iou;
}

int testIoU_compute( int argc, char *argv[] ){
	//test
	Box a{2,20,15,38};
	Box b{3,15,9,50};
	double iou=IoU_compute(a,b,false,false,false,true);
	std::cout<<"iou :"<<iou<<std::endl;
	
	return 1;
	
}
//将4角点旋转
Eigen::Matrix<float,4,3> box4CornerTransform(const Eigen::Matrix<float,4,3>& box4Corner, const float& yawDegree){
	Eigen::Affine3f transform = Eigen::Affine3f::Identity();
	transform.translation() << 0,0,0;
	transform.rotate(Eigen::AngleAxisf(yawDegree * M_PI / 180.0, Eigen::Vector3f::UnitZ()));
	
	Eigen::Matrix3f rotation = transform.rotation();
	Eigen::Matrix<float,4,3> box4CornerTransformed = box4Corner * rotation;
	return std::move(box4CornerTransformed);
}
//顺序：左上角开始顺时针得到0-1-2-3点
Eigen::Matrix<float,4,3> getBox4Corner(const Eigen::MatrixXd & objectInfo){
	detection d;
	d.x = objectInfo(0,0);
	d.y = objectInfo(0,1);
	d.w = objectInfo(0,6);
	d.h = objectInfo(0,7);
	d.theta = objectInfo(0,5) / M_PI * 180.0;
	
	Eigen::Matrix<float,4,3> box4Corner;//转成3维方便计算，TODO 二维平面旋转
	box4Corner<<static_cast<float>(d.x - d.w / 2.0), static_cast<float>(d.y + d.h / 2.0), 0,//左上角
				static_cast<float>(d.x + d.w / 2.0), static_cast<float>(d.y + d.h / 2.0), 0,//右上角
				static_cast<float>(d.x + d.w / 2.0), static_cast<float>(d.y - d.h / 2.0), 0,//右下角
				static_cast<float>(d.x - d.w / 2.0), static_cast<float>(d.y - d.h / 2.0), 0;//左下角
	Eigen::Matrix<float,4,3> box4CornerTransformed = box4CornerTransform(box4Corner, d.theta);
	return std::move(box4CornerTransformed);//std::move TODO
}

//只取对角的点0-2或者1-3,此处取1-3点
Box changeBoxStruct(const Eigen::Matrix<float,4,3>& box4Corner){
	//Eigen按列优先取值，按行优先存入
	Box boxDiagonalPoint{box4Corner(1),box4Corner(5),box4Corner(3),box4Corner(7)};
	return std::move(boxDiagonalPoint);
}

float caculate3DIou(double iou2D,const Eigen::MatrixXd &truth_poses, const Eigen::MatrixXd &landmark_poses){
	float iou_3d = 0;
	
	float tru_minz = truth_poses(0, 2) - truth_poses(0, 8);//0
	float tru_maxz = truth_poses(0, 2) + truth_poses(0, 8);//2
	
	float land_minz = landmark_poses(0, 2) - landmark_poses(0, 8);//0
	float land_maxz = landmark_poses(0, 2) + landmark_poses(0, 8);//2
	
	if (land_maxz <= tru_maxz && land_maxz > tru_minz && tru_maxz != land_minz) {
		float height_iou = (land_maxz - tru_minz) / (tru_maxz - land_minz);
		iou_3d = iou2D * height_iou;//底面积*高
	} else if (tru_maxz < land_maxz && tru_maxz > land_minz && land_maxz != tru_minz) {
		float height_iou = (tru_maxz - land_minz) / (land_maxz - tru_minz);
		iou_3d = iou2D * height_iou;
	}
	
	return std::move(iou_3d);
}

float runIOUCompute(const Eigen::MatrixXd &truth_poses, const Eigen::MatrixXd &landmark_poses){
	Eigen::Matrix<float,4,3> box4Corner_truth_poses = getBox4Corner(truth_poses);
	Eigen::Matrix<float,4,3> box4Corner_landmark_poses = getBox4Corner(landmark_poses);
	
	Box boxDiagonalPoint_truth_poses = changeBoxStruct(box4Corner_truth_poses);
	Box boxDiagonalPoint_landmark_poses = changeBoxStruct(box4Corner_landmark_poses);

	double iou_2d = IoU_compute(boxDiagonalPoint_truth_poses,boxDiagonalPoint_landmark_poses,false,false,false,true);
	std::cout<<"EIOU iou_2d :"<<iou_2d<<std::endl;

	float iou3D = 0;
	if(iou_2d <= 0){
        //std::cout << "WARN: 2D IOU score is " << iou_2d << ",return iou is " << " 0 " << std::endl;
        return 0.0;//3DIOU依赖2DIOU，2DIOU为0时，3DIOU为0
	}
	else{
		//std::cout << "INFO: 2D IOU score is " << iou_2d << std::endl;
		iou3D = caculate3DIou(iou_2d,truth_poses, landmark_poses);
		//std::cout << "INFO: 3D IOU score is " << iou3D << std::endl;
		return std::move(iou3D); 
	}
	return iou3D;
}
