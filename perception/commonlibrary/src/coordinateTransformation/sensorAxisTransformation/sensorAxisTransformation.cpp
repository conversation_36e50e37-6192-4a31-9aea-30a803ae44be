/********************************************************************************
* @author: shuangquan han
* @date: 2022/9/16 上午9:33
* @version: 1.0
* @description: 
********************************************************************************/


#include "sensorAxisTransformation.h"

#include <string>

SensorAxisTransformation::SensorAxisTransformation(){

};

//XYZEulerDegree
void SensorAxisTransformation::setSelfCarEulerDegrees(const Eigen::Vector3d& selfCarEulerDegrees){
	m_selfCarEulerDegrees = selfCarEulerDegrees;
	
	double selfCarRollRad = m_selfCarEulerDegrees(0) * M_PI / 180.0;
	double selfCarPitchRad = m_selfCarEulerDegrees(1) * M_PI / 180.0;
	double selfCarYawRad = -m_selfCarEulerDegrees(2) * M_PI / 180.0;

	Eigen::AngleAxisd angle2 = Eigen::AngleAxisd(selfCarYawRad,Eigen::Vector3d::UnitZ());
	Eigen::AngleAxisd angle1 = Eigen::AngleAxisd(selfCarPitchRad,Eigen::Vector3d::UnitY());
	Eigen::AngleAxisd angle0 = Eigen::AngleAxisd(selfCarRollRad,Eigen::Vector3d::UnitX());
	
	m_rotationFromZYX = angle0.matrix() * angle1.matrix() * angle2.matrix();
	m_rotationFromZXY = angle1.matrix() * angle0.matrix() * angle2.matrix();
	
	selfCarYawRad = -selfCarYawRad;
	angle2 = Eigen::AngleAxisd(selfCarYawRad,Eigen::Vector3d::UnitZ());
	m_rotationFromZXY_speed = angle1.matrix() * angle0.matrix() * angle2.matrix();
}

Eigen::Vector3d SensorAxisTransformation::getSelfCarEulerDegrees(){
	return m_selfCarEulerDegrees;
}

void SensorAxisTransformation::setSelfCarUTMPosition(const std::vector<double>& selfCarUTMPosition){
	m_selfCarUTMPosition = selfCarUTMPosition;
}



/***
 * UTM坐标系（ENU坐标系）转到imu坐标系（NED）20220920
 * @param inputposition imu坐标系下点坐标（NED）
 * @param eulerXYZDegree 组合导航设备输出的欧拉角-角度
 * @param translation	组合导航设备的UTM坐标
 * @param outputPosition 输出的UTM坐标
 */
void SensorAxisTransformation::utm2NED(const Eigen::Vector3d& inputposition,const Eigen::Vector3d& eulerXYZDegree,
                                                  const Eigen::Vector3d& translation,
                                                  Eigen::Vector3d& outputPosition){
	
	//ENU转NED， 20220920
	Eigen::Vector3d inputpositionNED;
	inputpositionNED[0] = inputposition[1] - translation[1];
	inputpositionNED[1] = inputposition[0] - translation[0];
	inputpositionNED[2] = -(inputposition[2] - translation[2]);
	
	//NED转IMU
	Eigen::Affine3d transform = Eigen::Affine3d::Identity();
	transform.translation() << translation[0],translation[1],translation[2];
	//只需旋转yaw
	wgs84_utm wgs84Utm;//20220914

	
	//20220915 NED转到IMU
	transform.rotate(Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(2)), Eigen::Vector3d::UnitZ()));
	transform.rotate(Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(1)), Eigen::Vector3d::UnitY()));
	transform.rotate(Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(0)), Eigen::Vector3d::UnitX()));
	//std::cout<<"transform before:\n"<<transform.rotation().matrix()<<std::endl;
	Eigen::MatrixX3d rotation = transform.rotation().matrix().inverse();
	//std::cout<<"transform after:\n"<<rotation.matrix()<<std::endl;
	
	
	outputPosition(0) = rotation(0,0) * inputpositionNED(0) + rotation(0,1) * inputpositionNED(1) + rotation(0,2) * inputpositionNED(2);
	outputPosition(1) = rotation(1,0) * inputpositionNED(0) + rotation(1,1) * inputpositionNED(1) + rotation(1,2) * inputpositionNED(2) ;
	outputPosition(2) = rotation(2,0) * inputpositionNED(0) + rotation(2,1) * inputpositionNED(1) + rotation(2,2) * inputpositionNED(2);
	
}


/***
 * imu坐标系（NED）转到UTM坐标系（ENU坐标系）20220920
 * @param inputposition imu坐标系下点坐标（NED）
 * @param eulerXYZDegree 组合导航设备输出的欧拉角-角度
 * @param translation	组合导航设备的UTM坐标
 * @param outputPosition 输出的UTM坐标
 */
void SensorAxisTransformation::transformPoint2utm(const Eigen::Vector3d& inputposition,const Eigen::Vector3d& eulerXYZDegree,
                                                  const Eigen::Vector3d& translation,Eigen::Vector3d& outputPosition){
	wgs84_utm wgs84Utm;//20220914
	Eigen::AngleAxisd angle2 = Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(2)),Eigen::Vector3d::UnitZ());
	Eigen::AngleAxisd angle1 = Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(1)),Eigen::Vector3d::UnitY());
	Eigen::AngleAxisd angle0 = Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(0)),Eigen::Vector3d::UnitX());
	Eigen::Matrix3d rotationFrom;
	rotationFrom = angle0.matrix() * angle1.matrix() * angle2.matrix();//ZYX
	
	Eigen::Vector3d tempPosition;
	//imu 转到NED
	tempPosition(0) = rotationFrom(0,0) * inputposition(0) + rotationFrom(0,1) * inputposition(1) + rotationFrom(0,2) * inputposition(2);
	tempPosition(1) = rotationFrom(1,0) * inputposition(0) + rotationFrom(1,1) * inputposition(1) + rotationFrom(1,2) * inputposition(2) ;
	tempPosition(2) = rotationFrom(2,0) * inputposition(0) + rotationFrom(2,1) * inputposition(1) + rotationFrom(2,2) * inputposition(2);
	
	//NED转ENU，再加上UTM坐标系 20220920
	outputPosition[0] = tempPosition(1) + translation[0];
	outputPosition[1] = tempPosition(0) + translation[1];
	outputPosition[2] = -tempPosition(2) + translation[2];
}

/***
 * 点云坐标转换
 * @param inputposition 输入原坐标系下三维点坐标
 * @param eulerXYZDegree 欧拉角（角度）
 * @param translation 平移向量（米）
 * @param outputPosition 输出坐标系下点坐标
 */
void SensorAxisTransformation::transformPoint(const Eigen::Vector3d& inputposition,const Eigen::Vector3d& eulerXYZDegree,
                                              const Eigen::Vector3d& translation,const std::string& transformOrder,
                                              Eigen::Vector3d& outputPosition){
	
	// cout<<"[确认GPS]transformPoint: rpy ："<<eulerXYZDegree[0]<<","<<eulerXYZDegree[1]<<","<<eulerXYZDegree[2]
	//     <<", m_selfCarEulerDegrees: rpy ："<<m_selfCarEulerDegrees[0] <<","<<m_selfCarEulerDegrees[1] <<","<<m_selfCarEulerDegrees[2] <<endl;
	
	
	wgs84_utm wgs84Utm;
	Eigen::AngleAxisd angle2 = Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(2)),Eigen::Vector3d::UnitZ());
	Eigen::AngleAxisd angle1 = Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(1)),Eigen::Vector3d::UnitY());
	Eigen::AngleAxisd angle0 = Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(0)),Eigen::Vector3d::UnitX());
	Eigen::Matrix3d rotationFrom;
	
	if(transformOrder == "ZYX"){
		rotationFrom = angle0.matrix() * angle1.matrix() * angle2.matrix();
	}
	else if(transformOrder == "ZXY"){
		rotationFrom = angle1.matrix() * angle0.matrix() * angle2.matrix();
		
	}
	else{
		std::cerr << "Error:: not complish transform : " << transformOrder << std::endl;
	}
	//Eigen::Affine3d transform(rotationFrom);
	//transform.translation() << translation[0],translation[1],translation[2];
	outputPosition = rotationFrom * inputposition + translation;
	//outputPosition(0) = rotationFrom(0,0) * inputposition(0) + rotationFrom(0,1) * inputposition(1) + rotationFrom(0,2) * inputposition(2) + translation[0];
	//outputPosition(1) = rotationFrom(1,0) * inputposition(0) + rotationFrom(1,1) * inputposition(1) + rotationFrom(1,2) * inputposition(2) + translation[1];
	//outputPosition(2) = rotationFrom(2,0) * inputposition(0) + rotationFrom(2,1) * inputposition(1) + rotationFrom(2,2) * inputposition(2) + translation[2];
}

void SensorAxisTransformation::transformPoint(const Eigen::Vector3d& inputposition,
                                              const Eigen::Vector3d& translation,const std::string& transformOrder,
                                              Eigen::Vector3d& outputPosition){
	
	//cout<<"[确认GPS]transformPoint: "
	//	//<<eulerXYZDegree[0]<<","<<eulerXYZDegree[1]<<","<<eulerXYZDegree[2]
	//    <<", m_selfCarEulerDegrees: rpy ："<<m_selfCarEulerDegrees[0] <<","<<m_selfCarEulerDegrees[1] <<","<<m_selfCarEulerDegrees[2] <<endl;
	
	if(transformOrder == "ZYX"){
		outputPosition = m_rotationFromZYX * inputposition + translation;
		// outputPosition(0) = m_rotationFromZYX(0,0) * inputposition(0) + m_rotationFromZYX(0,1) * inputposition(1) + m_rotationFromZYX(0,2) * inputposition(2) + translation[0];
		// outputPosition(1) = m_rotationFromZYX(1,0) * inputposition(0) + m_rotationFromZYX(1,1) * inputposition(1) + m_rotationFromZYX(1,2) * inputposition(2) + translation[1];
		// outputPosition(2) = m_rotationFromZYX(2,0) * inputposition(0) + m_rotationFromZYX(2,1) * inputposition(1) + m_rotationFromZYX(2,2) * inputposition(2) + translation[2];
	}
	else if(transformOrder == "ZXY"){
		outputPosition = m_rotationFromZXY * inputposition + translation;
		// outputPosition(0) = m_rotationFromZXY(0,0) * inputposition(0) + m_rotationFromZXY(0,1) * inputposition(1) + m_rotationFromZXY(0,2) * inputposition(2) + translation[0];
		// outputPosition(1) = m_rotationFromZXY(1,0) * inputposition(0) + m_rotationFromZXY(1,1) * inputposition(1) + m_rotationFromZXY(1,2) * inputposition(2) + translation[1];
		// outputPosition(2) = m_rotationFromZXY(2,0) * inputposition(0) + m_rotationFromZXY(2,1) * inputposition(1) + m_rotationFromZXY(2,2) * inputposition(2) + translation[2];

	}
	else if (transformOrder == "ZXY_speed"){
		outputPosition = m_rotationFromZXY_speed * inputposition + translation;
	}
	else{
		std::cerr << "Error:: not complish transform : " << transformOrder << std::endl;
	}
}


/***
 * 20221111 点云坐标转换
 * @param inputposition 输入原坐标系下三维点坐标
 * @param quaternion 旋转四元数
 * @param translation 平移向量（米）
 * @param outputPosition 输出坐标系下点坐标
 */
void SensorAxisTransformation::transformPoint(const Eigen::Vector3f &inputposition, const Eigen::Quaternion<float> &quaternion,
											  const Eigen::Vector3f &translation, Eigen::Vector3f &outputPosition)
{
	Eigen::Affine3f transform = Eigen::Affine3f::Identity();
	transform.translation() << translation[0], translation[1], translation[2];
	transform.rotate(quaternion);

	outputPosition(0) = transform(0, 0) * inputposition(0) + transform(0, 1) * inputposition(1) + transform(0, 2) * inputposition(2) + transform(0, 3);
	outputPosition(1) = transform(1, 0) * inputposition(0) + transform(1, 1) * inputposition(1) + transform(1, 2) * inputposition(2) + transform(1, 3);
	outputPosition(2) = transform(2, 0) * inputposition(0) + transform(2, 1) * inputposition(1) + transform(2, 2) * inputposition(2) + transform(2, 3);
}


/***
 * utm转到载体坐标系（INS570D）
 * @param positionUTM
 * @param eulerXYZDegree
 * @param utmInCarrierAxis
 */
void SensorAxisTransformation::utmAxis2CarrierAxis(const Eigen::Vector3d& positionUTM,const Eigen::Vector3d& eulerXYZDegree,Eigen::Vector3d& utmInCarrierAxis){
	Eigen::Affine3d transform = Eigen::Affine3d::Identity();
	//只需旋转yaw
	wgs84_utm wgs84Utm;//20220914
	//20220915
	transform.rotate(Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(2)), Eigen::Vector3d::UnitZ()));
	
	transform.rotate(Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(1)), Eigen::Vector3d::UnitY()));
	transform.rotate(Eigen::AngleAxisd(wgs84Utm.DegToRad(eulerXYZDegree(0)), Eigen::Vector3d::UnitX()));
	
	//Eigen::Matrix3d rotation = transform.rotation().matrix();
	utmInCarrierAxis(0) = transform(0,0) * positionUTM(0) + transform(0,1) * positionUTM(1) + transform(0,2) * positionUTM(2) +transform(0,3);
	utmInCarrierAxis(1) = transform(1,0) * positionUTM(0) + transform(1,1) * positionUTM(1) + transform(1,2) * positionUTM(2) + transform(1,3);
	utmInCarrierAxis(2) = transform(2,0) * positionUTM(0) + transform(2,1) * positionUTM(1) + transform(2,2) * positionUTM(2) + transform(2,3);
}

/***
 * 载体坐标系（INS570D）转到lidar
 * @param inputPosition
 * @param outputPosition
 */
void SensorAxisTransformation::transINS570D_to_lidar80(const Eigen::Vector3d& inputPosition,Eigen::Vector3d& outputPosition){
	Eigen::Matrix4d transform;//INS570D_to_lidar80
	transform << 0,1,0,0,
		1,0,0,-1.1,
		0,0,-1,1.4,
		0,0,0,1;

	outputPosition(0) = transform(0,0) * inputPosition(0) + transform(0,1) * inputPosition(1) + transform(0,2) * inputPosition(2) +transform(0,3);
	outputPosition(1) = transform(1,0) * inputPosition(0) + transform(1,1) * inputPosition(1) + transform(1,2) * inputPosition(2) + transform(1,3);
	outputPosition(2) = transform(2,0) * inputPosition(0) + transform(2,1) * inputPosition(1) + transform(2,2) * inputPosition(2) + transform(2,3);
	
}

/*** 20221021
 * utm坐标下点转到lidar坐标系下，需要知道当前lidar的经纬度
 * @param inputposition lidar检测目标的UTM坐标
 * @param eulerXYZDegree 自车组合导航设备输出的欧拉角-角度（组合导航前右下坐标系）
 * @param selfCarUTMPosition 自车组合导航设备的UTM坐标（组合导航前右下坐标系）
 * @return lidar坐标系下的坐标（右前上坐标系）
 */
Eigen::Vector3d SensorAxisTransformation::utm2LidarAxis(const Eigen::Vector3d& inputposition,const Eigen::Vector3d& eulerXYZDegree,
                                             const Eigen::Vector3d& selfCarUTMPosition){
	Eigen::Vector3d positionInNED;//1.8 车高
	utm2NED(inputposition,eulerXYZDegree,selfCarUTMPosition,positionInNED);//degree
	//std::cout<<"positionInNED-utm2LidarAxis: "<<positionInNED[0]<<", "<<positionInNED[1]<<", "<<positionInNED[2]<<std::endl;
	//NED(IMU)转lidar
	Eigen::Vector3d imu2LidarEulerDegree{-0.871000,0.568000,0.125000};//红旗2-0.871000,0.568000,0.125000  红旗1：180 0 90  lidar检测目标在后轴中心位置X右Y前Z上做坐标 组合导航输出车后轴壳体坐标X前Y右Z下：XYZ178.9,-1.46,-90.28
	Eigen::Vector3d imu2LidarTranlation{-0.302299,-1.690385,-1.231000};// 红旗2：-0.302299,-1.690385,-1.231000 红旗1：1.1,0.0,-1.4
	Eigen::Vector3d objectPositionInLidarAxis;
	transformPoint(positionInNED,imu2LidarEulerDegree,imu2LidarTranlation,"ZYX", objectPositionInLidarAxis);//转到lidar坐标系下
	//std::cout<<"objectPositionInLidarAxis-utm2LidarAxis: "<<objectPositionInLidarAxis[0]<<", "<<objectPositionInLidarAxis[1]<<", "<<objectPositionInLidarAxis[2]<<std::endl;
	return std::move(objectPositionInLidarAxis);
}


/*** 将lidar坐标系下的目标转到UTM坐标
 * 20221024
 * @param inputposition 输入lidar坐标系下坐标XYZ
 * @param eulerXYZDegree 组合导航设备输出的欧拉角-角度
 * @param selfCarUTMPosition 自车UTM坐标
 * @return UTM坐标系下目标坐标
 */
Eigen::Vector3d SensorAxisTransformation::lidar2UTMAxis(const Eigen::Vector3d& inputposition,const Eigen::Vector3d& eulerXYZDegree,
                                                        const Eigen::Vector3d& selfCarUTMPosition){
	//std::cout<<"lidar2UTMAxis：................\n";
	SensorAxisTransformation sensorAxisTransformation;
	Eigen::Vector3d positionInLidar{inputposition[0],inputposition[1],inputposition[2] - 1.8};//1.8 车高 TODO  其他地方用到注意更改inputposition[2] - 1.8
	Eigen::Vector3d lidar2ImuEulerDegree{180,0.0,90.0};//180 0 90  lidar检测目标在后轴中心位置X右Y前Z上做坐标 组合导航输出车后轴壳体坐标X前Y右Z下：XYZ178.9,-1.46,90.28
	Eigen::Vector3d lidar2ImuTranlation{0.9,0.0,-1.4};//1.1,0.0,-1.4
	//std::cout<<"lidar2UTMAxis2：................\n";
	Eigen::Vector3d objectPositionInImuAxis;
	sensorAxisTransformation.transformPoint(positionInLidar,
	                                        lidar2ImuEulerDegree,
	                                        lidar2ImuTranlation,
											"ZYX",
	                                        objectPositionInImuAxis);//lidar坐标系下目标转到IMU坐标系
	//cout<<"lidar坐标系下目标位置-id："<<(int)this->m_id + 1<<", "<<positionInLidar[0]<<","<<positionInLidar[1]<<","<<positionInLidar[2]<<",\n"
	//    <<"imu坐标下目标位置-id："<<(int)this->m_id + 1<<", "<<objectPositionInImuAxis(0)<<","<<objectPositionInImuAxis(1)<<","<<objectPositionInImuAxis(2)<<endl;
	//std::cout<<"lidar坐标系下目标位置："<<""<<positionInLidar[0]<<","<<positionInLidar[1]<<","<<positionInLidar[2]<<std::endl;
	
	//NED转到UTM
	Eigen::Vector3d objectPositionInUTMAxis;
	sensorAxisTransformation.transformPoint2utm(objectPositionInImuAxis,
	                                            Eigen::Vector3d{eulerXYZDegree[0],eulerXYZDegree[1], eulerXYZDegree[2]},//curGPSMsg.roll,curGPSMsg.pitch,curGPSMsg.heading
	                                            Eigen::Vector3d{selfCarUTMPosition[0],selfCarUTMPosition[1],selfCarUTMPosition[2]},
	                                            objectPositionInUTMAxis);//imu坐标系下目标转到UTM坐标系
	//std::cout<<"UTM坐标系下目标位置："<<objectPositionInUTMAxis(0)<<","<<objectPositionInUTMAxis(1)<<","<<objectPositionInUTMAxis(2)<<std::endl;
	return std::move(objectPositionInUTMAxis);
}

/***
 * 载体坐标系到ENU坐标系
 * @param bodyAxisPosition
 * @param selfCarHeadingEulerDegrees
 * @return
 */
std::vector<double> SensorAxisTransformation::bodyAxis2ENU(const Eigen::Vector3d& bodyAxisPosition,
                                 const Eigen::Vector3d& selfCarHeadingEulerDegrees){
	//snesorgps中的IMU已经转到车后轴中心FRD坐标系
	Eigen::Vector3d bodyAxis2ENUDegree{0, 0, -selfCarHeadingEulerDegrees[2]};//ENU与UTM只差一个航向角
	Eigen::Vector3d bodyAxis2ENUTranlation{0.0, 0.0, 0}; //
	
	Eigen::Vector3d objectPositionInENUAxis;
	transformPoint(bodyAxisPosition,
	               bodyAxis2ENUDegree,
	               bodyAxis2ENUTranlation,
	               "ZYX",
	               objectPositionInENUAxis);//object已经转到车后轴中心ENU坐标系，红旗1 ZYX顺序旋转
	return {objectPositionInENUAxis[0], objectPositionInENUAxis[1], objectPositionInENUAxis[2]};
}

std::vector<double> SensorAxisTransformation::bodyAxis2ENU(const Eigen::Vector3d& bodyAxisPosition){
	//snesorgps中的IMU已经转到车后轴中心FRD坐标系
	//Eigen::Vector3d bodyAxis2ENUDegree{0, 0, -m_selfCarEulerDegrees[2]};//ENU与UTM只差一个航向角
	Eigen::Vector3d bodyAxis2ENUTranlation{0.0, 0.0, 0}; //
	
	//cout<<"[确认GPS]bodyAxis2ENU: rpy ："<<endl;
	Eigen::Vector3d objectPositionInENUAxis;
	transformPoint(bodyAxisPosition,
	               //bodyAxis2ENUDegree,
	               bodyAxis2ENUTranlation,
	               "ZYX",
	               objectPositionInENUAxis);//object已经转到车后轴中心ENU坐标系，红旗1 ZYX顺序旋转
	return {objectPositionInENUAxis[0], objectPositionInENUAxis[1], objectPositionInENUAxis[2]};
}
/***
 * ENU坐标系到载体坐标系
 * @param bodyAxisPosition
 * @param selfCarHeadingEulerDegrees
 * @return
 */
std::vector<double> SensorAxisTransformation::ENU2BodyAxis(const Eigen::Vector3d& ENUAxisPosition,
                                                           const Eigen::Vector3d& selfCarHeadingEulerDegrees){
	//snesorgps中的IMU已经转到车后轴中心FRD坐标系
	Eigen::Vector3d ENU2BodyAxisDegree{0, 0, selfCarHeadingEulerDegrees[2]};//ENU与UTM只差一个航向角
	Eigen::Vector3d ENU2BodyAxisTranlation{0.0, 0.0, 0}; //
	
	Eigen::Vector3d objectPositionInBodyAxis;
	transformPoint(ENUAxisPosition,
	               ENU2BodyAxisDegree,
	               ENU2BodyAxisTranlation,
	               "ZXY",
	               objectPositionInBodyAxis);//object已经转到车后轴中心ENU坐标系，红旗1 ZYX顺序旋转
	return {objectPositionInBodyAxis[0], objectPositionInBodyAxis[1], objectPositionInBodyAxis[2]};
}

std::vector<double> SensorAxisTransformation::ENU2BodyAxis(const Eigen::Vector3d& ENUAxisPosition){
	//snesorgps中的IMU已经转到车后轴中心FRD坐标系
	//Eigen::Vector3d ENU2BodyAxisDegree{0, 0, selfCarHeadingEulerDegrees[2]};//ENU与UTM只差一个航向角
	Eigen::Vector3d ENU2BodyAxisTranlation{0.0, 0.0, 0}; //
	
	Eigen::Vector3d objectPositionInBodyAxis;
	transformPoint(ENUAxisPosition,
	               //ENU2BodyAxisDegree,
	               ENU2BodyAxisTranlation,
	               "ZXY_speed",
	               objectPositionInBodyAxis);//object已经转到车后轴中心ENU坐标系，红旗1 ZYX顺序旋转
	return {objectPositionInBodyAxis[0], objectPositionInBodyAxis[1], objectPositionInBodyAxis[2]};
}


/***
 * 车后轴中心ENU坐标转到UTM--验证过
 * @param ENUPosition  目标的坐标(车后轴中心ENU坐标-XYZ)
 * @param selfCarHeadingEulerDegrees 车后轴中心欧拉角-XYZ
 * @param selfCarUTMPosition 自车的UTM坐标
 * @return 目标在UTM坐标系下的坐标
 */
std::vector<double> SensorAxisTransformation::ENU2UTM(const Eigen::Vector3d& bodyAxisPosition,
                                                      const Eigen::Vector3d& selfCarHeadingEulerDegrees,
                                                      const std::vector<double>& selfCarUTMPosition){
	std::vector<double> objectPositionInENUAxis = bodyAxis2ENU(bodyAxisPosition, selfCarHeadingEulerDegrees);
	//cout<<"转换前objectInLidarAxis："<<ENUPosition[0]<<","<<ENUPosition[1]<<","<<ENUPosition[2]<<endl;
	//cout<<"objectPositionInENUAxis："<<objectPositionInENUAxis(0)<<","<<objectPositionInENUAxis(1)<<","<<objectPositionInENUAxis(2)<<endl;
	std::vector<double> objectPositionInUTMAxis = {objectPositionInENUAxis[0] + selfCarUTMPosition[0],
	                                               objectPositionInENUAxis[1] + selfCarUTMPosition[1],
	                                               objectPositionInENUAxis[2] + selfCarUTMPosition[2]};
	return objectPositionInUTMAxis;
	//cout<<"debug:转换到UTM坐标："<<objectPositionInUTMAxis(0)<<","<<objectPositionInUTMAxis(1)<<","<<objectPositionInUTMAxis(2)<<endl;
	
}

std::vector<double> SensorAxisTransformation::ENU2UTM(const Eigen::Vector3d& bodyAxisPosition){
	std::vector<double> objectPositionInENUAxis = bodyAxis2ENU(bodyAxisPosition);
	//cout<<"objectPositionInENUAxis："<<objectPositionInENUAxis(0)<<","<<objectPositionInENUAxis(1)<<","<<objectPositionInENUAxis(2)<<endl;
	std::vector<double> objectPositionInUTMAxis = {objectPositionInENUAxis[0] + m_selfCarUTMPosition[0],
	                                               objectPositionInENUAxis[1] + m_selfCarUTMPosition[1],
	                                               objectPositionInENUAxis[2] + m_selfCarUTMPosition[2]};
	return std::move(objectPositionInUTMAxis);
	//cout<<"debug:转换到UTM坐标："<<objectPositionInUTMAxis(0)<<","<<objectPositionInUTMAxis(1)<<","<<objectPositionInUTMAxis(2)<<endl;
	
}

 /***
  * 车后轴中心ENU坐标转到经纬度--验证过
  * @param ENUPosition  目标的坐标(车后轴中心ENU坐标-XYZ)
  * @param selfCarHeadingEulerDegrees 车后轴中心欧拉角-XYZ
  * @param selfCarUTMPosition 自车的UTM坐标
  * @param cityUTMCode 自车所在的UTM编号
  * @param lla 经纬度
  */
void SensorAxisTransformation::ENU2LLA(const Eigen::Vector3d& ENUPosition,
									   const Eigen::Vector3d& selfCarHeadingEulerDegrees,
									   const std::vector<double>& selfCarUTMPosition,
									   int cityUTMCode,
									   WGS84Corr& lla){
	//ENU转到UTM坐标
	 std::vector<double> objectPositionInUTMAxis = ENU2UTM(ENUPosition,selfCarHeadingEulerDegrees,
	                                                        selfCarUTMPosition);
	//经纬度
	wgs84_utm wgs84Utm;
	wgs84Utm.UTMXYToLatLon(objectPositionInUTMAxis[0] , objectPositionInUTMAxis[1], cityUTMCode, false, lla);
}

/***
 * 将车后轴的UTM坐标系下的坐标转到车后轴的RFU坐标系下的坐标
 * @param inputUTMPosition 目标：车后轴的UTM坐标系下的坐标
 * @param selfCarEulerXYZDegree 车的欧拉角Degree-XYZ(其中Z的值为正北方向顺时针0~360度)
 * @param selfCarUTMPosition	车的UTM坐标
 * @return 目标在车后轴的RFU坐标系下的坐标
 */
void SensorAxisTransformation::utm2CarBackRFU(const Eigen::Vector3d& inputUTMPosition,const Eigen::Vector3d& selfCarEulerXYZDegree,
                                             const Eigen::Vector3d& selfCarUTMPosition, Eigen::Vector3d& objectPositionInCarBackFRU){
	
	//
	//cout<<"inputUTMPosition："<<inputUTMPosition[0]<<","<<inputUTMPosition[1]<<","<<inputUTMPosition[2]
	//	<<",\n"<<selfCarEulerXYZDegree(0)<<","<<selfCarEulerXYZDegree(1)<<","<<selfCarEulerXYZDegree(2)
	//	<< ",\n"<<selfCarUTMPosition(0)<<","<<selfCarUTMPosition(1)<<","<<selfCarUTMPosition(2)<<endl;

	Eigen::Vector3d inputPositionENUInNorthHeading;
	inputPositionENUInNorthHeading[0] = inputUTMPosition[0] - selfCarUTMPosition[0];
	inputPositionENUInNorthHeading[1] = inputUTMPosition[1] - selfCarUTMPosition[1];
	inputPositionENUInNorthHeading[2] = inputUTMPosition[2] - selfCarUTMPosition[2];
	//cout<<"inputPositionENUInNorthHeading："<<inputPositionENUInNorthHeading[0]<<","<<inputPositionENUInNorthHeading[1]<<","<<inputPositionENUInNorthHeading[2]
	//    <<endl;
	
	//
	//Eigen::Affine3d transform = Eigen::Affine3d::Identity();
	////transform.translation() << selfCarUTMPosition[0],selfCarUTMPosition[1],selfCarUTMPosition[2];
	////cout<<"11111111111111111111111111111111：\n";
	////inputPositionENUInNorthHeading 转到lidar ENU坐标系
	//transform.rotate(Eigen::AngleAxisd(-selfCarEulerXYZDegree(2) * M_PI / 180.0, Eigen::Vector3d::UnitZ()));
	//transform.rotate(Eigen::AngleAxisd(selfCarEulerXYZDegree(0) * M_PI / 180.0, Eigen::Vector3d::UnitX()));
	//transform.rotate(Eigen::AngleAxisd(selfCarEulerXYZDegree(1) * M_PI / 180.0, Eigen::Vector3d::UnitY()));
	//Eigen::MatrixX3d rotation = transform.rotation().matrix().inverse();
	
	Eigen::MatrixX3d rotation = Eigen::AngleAxisd(selfCarEulerXYZDegree(2) * M_PI / 180.0, Eigen::Vector3d::UnitZ()).matrix()
			* Eigen::AngleAxisd(selfCarEulerXYZDegree(0) * M_PI / 180.0, Eigen::Vector3d::UnitX()).matrix()
			* Eigen::AngleAxisd(selfCarEulerXYZDegree(1) * M_PI / 180.0, Eigen::Vector3d::UnitY()).matrix();
	Eigen::Vector3d objectPositionInCarCenterFRU;
	objectPositionInCarCenterFRU(0) = rotation(0,0) * inputPositionENUInNorthHeading(0) + rotation(0,1) * inputPositionENUInNorthHeading(1) + rotation(0,2) * inputPositionENUInNorthHeading(2);
	objectPositionInCarCenterFRU(1) = rotation(1,0) * inputPositionENUInNorthHeading(0) + rotation(1,1) * inputPositionENUInNorthHeading(1) + rotation(1,2) * inputPositionENUInNorthHeading(2) ;
	objectPositionInCarCenterFRU(2) = rotation(2,0) * inputPositionENUInNorthHeading(0) + rotation(2,1) * inputPositionENUInNorthHeading(1) + rotation(2,2) * inputPositionENUInNorthHeading(2);
	
	objectPositionInCarBackFRU[0] = objectPositionInCarCenterFRU[0];
	objectPositionInCarBackFRU[1] = objectPositionInCarCenterFRU[1];//车中心点RFU转到车后轴RFU
	objectPositionInCarBackFRU[2] = objectPositionInCarCenterFRU[2];
	//return objectPositionInCarBackFRU;
	
}


//TODO  验证
/***
 * ENU速度转成lidar坐标系（RFU）速度
 * @param curGPSEulerRPYAngleDegree
 * @param curGPSENUSpeed
 * @return
 */
//Eigen::Vector3d positionENU2CarBackRFUAxis(const Eigen::Vector3d& curGPSEulerRPYAngleDegree, const Eigen::Vector3d& positionInENU){
//	Eigen::Vector3d euler_angle_b2e(curGPSEulerRPYAngleDegree[0] * M_PI / 180.0, curGPSEulerRPYAngleDegree[1] * M_PI / 180.0,
//	                                curGPSEulerRPYAngleDegree[2] * M_PI / 180.0); // 从b系到E系的欧拉角
//	Eigen::Matrix3d rotation_matrix_b2e;  // 从b系到E系的旋转矩阵
//	// Eigen::Matrix3d rotation_matrix_e2b;  // 从E系到b系的旋转矩阵
//
//	Eigen::Vector3d position_e(positionInENU[0], positionInENU[1], positionInENU[2]);   // E系下的速度
//	Eigen::Vector3d position_b;           // b系下的速度
//	// 计算旋转矩阵 Z-X-Y
//	rotation_matrix_b2e = Eigen::AngleAxisd(euler_angle_b2e[1], Eigen::Vector3d::UnitY())
//	                      * Eigen::AngleAxisd(euler_angle_b2e[0], Eigen::Vector3d::UnitX())
//	                      * Eigen::AngleAxisd(euler_angle_b2e[2], Eigen::Vector3d::UnitZ());//
//
//	// rotation_matrix_e2b = rotation_matrix_b2e.transpose();
//	// 将E系下位置转到b系（右前上）
//	position_b = rotation_matrix_b2e * position_e;
//	// 给出车辆运动速度（理论上应该有从b系到车辆坐标系的转换过程，但是这里没有精确的标定值，暂时忽略）
//
//	return std::move(position_b);
//}


/***
 * ENU信息（速度或者位置）转成车后轴坐标系（右前上）
 * @param curGPSEulerRPYAngleDegree
 * @param curGPSENUSpeed
 * @return
 */
// std::vector<double> SensorAxisTransformation::ENU2CarBackRFUAxis(const Eigen::Vector3d& curGPSEulerRPYAngleDegree,
// 																 const Eigen::Vector3d& curGPSENUSpeed){
// 	bool isTest = true;
// 	if(!isTest){
// 		Eigen::Vector3d euler_angle_b2e(curGPSEulerRPYAngleDegree[0] * M_PI / 180.0, curGPSEulerRPYAngleDegree[1] * M_PI / 180.0,
// 	                                curGPSEulerRPYAngleDegree[2] * M_PI / 180.0); // 从b系到E系的欧拉角
// 		Eigen::Vector3d velocity_e(curGPSENUSpeed[0], curGPSENUSpeed[1], curGPSENUSpeed[2]);   // E系下的速度
// 		Eigen::Vector3d velocity_b;           // b系下的速度
// 		// 计算旋转矩阵 Z-X-Y
// 		Eigen::Matrix3d rotation_matrix_b2e;  // 从b系到E系的旋转矩阵
// 		// Eigen::Matrix3d rotation_matrix_e2b;  // 从E系到b系的旋转矩阵

// 		//rotation_matrix_b2e = Eigen::AngleAxisd(euler_angle_b2e[1], Eigen::Vector3d::UnitY())
// 		//                      * Eigen::AngleAxisd(euler_angle_b2e[0], Eigen::Vector3d::UnitX())
// 		//                      * Eigen::AngleAxisd(euler_angle_b2e[2], Eigen::Vector3d::UnitZ());//npos TODO 报错
		
// 		rotation_matrix_b2e = Eigen::AngleAxisd(euler_angle_b2e[1], Eigen::Vector3d::UnitY()).toRotationMatrix()
// 							* Eigen::AngleAxisd(euler_angle_b2e[0], Eigen::Vector3d::UnitX()).toRotationMatrix()
// 							* Eigen::AngleAxisd(euler_angle_b2e[2], Eigen::Vector3d::UnitZ()).toRotationMatrix();//npos TODO 根据红旗2坐标更改
		
// 		// rotation_matrix_e2b = rotation_matrix_b2e.transpose();
// 		// 将E系下速度转到b系（右前上）
// 		velocity_b = rotation_matrix_b2e * velocity_e;
// 		// 给出车辆运动速度（理论上应该有从b系到车辆坐标系的转换过程，但是这里没有精确的标定值，暂时忽略）
		
// 		std::vector<double> infoInCarBackRFU{velocity_b[0], velocity_b[1], velocity_b[2]};
		
// 		return infoInCarBackRFU;
// 	}
// 	else{
// 		// 提升性能
// 		Eigen::Vector3d euler_angle_b2e = curGPSEulerRPYAngleDegree.array().cast<double>() * M_PI / 180.0; // 从b系到E系的欧拉角
// 		Eigen::Vector3d velocity_e = curGPSENUSpeed.array().cast<double>(); // E系下的速度
// 		// 计算旋转矩阵 Z-X-Y
// 		Eigen::Matrix3d rotation_matrix_b2e;  // 从b系到E系的旋转矩阵
// 		// Eigen::Matrix3d rotation_matrix_e2b;  // 从E系到b系的旋转矩阵

// 		rotation_matrix_b2e = Eigen::AngleAxisd(euler_angle_b2e[1], Eigen::Vector3d::UnitY())
// 							* Eigen::AngleAxisd(euler_angle_b2e[0], Eigen::Vector3d::UnitX())
// 							* Eigen::AngleAxisd(euler_angle_b2e[2], Eigen::Vector3d::UnitZ());
// 		// rotation_matrix_e2b = rotation_matrix_b2e.transpose();
// 		// 将E系下速度转到b系（右前上）
// 		Eigen::Vector3d velocity_b = rotation_matrix_b2e * velocity_e;
// 		// 给出车辆运动速度（理论上应该有从b系到车辆坐标系的转换过程，但是这里没有精确的标定值，暂时忽略）
		
// 		std::vector<double> infoInCarBackRFU{velocity_b[0], velocity_b[1], velocity_b[2]};
		
// 		return std::move(infoInCarBackRFU);
// 	}
// }

// 使用每帧传入的GPS信息（避免每个目标都传入）
std::vector<double> SensorAxisTransformation::ENU2CarBackRFUAxis(const Eigen::Vector3d& curGPSEulerRPYAngleDegree,
																 const Eigen::Vector3d& curGPSENUSpeed){
	// 提升性能
	Eigen::Vector3d euler_angle_b2e = curGPSEulerRPYAngleDegree * M_PI / 180.0; // 从b系到E系的欧拉角
	Eigen::Vector3d velocity_e = curGPSENUSpeed; // E系下的速度
	// 计算旋转矩阵 Z-X-Y
	Eigen::Matrix3d rotation_matrix_b2e;  // 从b系到E系的旋转矩阵
	// Eigen::Matrix3d rotation_matrix_e2b;  // 从E系到b系的旋转矩阵

	rotation_matrix_b2e = Eigen::AngleAxisd(euler_angle_b2e[1], Eigen::Vector3d::UnitY())
						* Eigen::AngleAxisd(euler_angle_b2e[0], Eigen::Vector3d::UnitX())
						* Eigen::AngleAxisd(euler_angle_b2e[2], Eigen::Vector3d::UnitZ());
	// rotation_matrix_e2b = rotation_matrix_b2e.transpose();
	// 将E系下速度转到b系（右前上）
	Eigen::Vector3d velocity_b = rotation_matrix_b2e * velocity_e;
	// 给出车辆运动速度（理论上应该有从b系到车辆坐标系的转换过程，但是这里没有精确的标定值，暂时忽略）
	
	std::vector<double> infoInCarBackRFU{velocity_b[0], velocity_b[1], velocity_b[2]};
	
	return std::move(infoInCarBackRFU);
}


/***
 * ENU信息（速度或者位置）转成车后轴坐标系（前左上）
 * @param curGPSEulerRPYAngleDegree
 * @param curGPSENUSpeed
 * @return
 */
std::vector<double> SensorAxisTransformation::ENU2CarBackFLUAxis(const Eigen::Vector3d& curGPSEulerRPYAngleDegree, const Eigen::Vector3d& curGPSENUSpeed){
	std::vector<double> infoInCarBackRFU = ENU2CarBackRFUAxis(curGPSEulerRPYAngleDegree, curGPSENUSpeed);
	return {infoInCarBackRFU[1], -infoInCarBackRFU[0], infoInCarBackRFU[2]};
}

std::vector<double> SensorAxisTransformation::carBackRFUAxis2ENU(const Eigen::Vector3d& curGPSEulerRPYAngleDegree, const Eigen::Vector3d& carBackRFUSpeed){
	Eigen::Vector3d euler_angle_b2e = curGPSEulerRPYAngleDegree * M_PI / 180.0;//(curGPSEulerRPYAngleDegree[0] * M_PI / 180.0, curGPSEulerRPYAngleDegree[1] * M_PI / 180.0, curGPSEulerRPYAngleDegree[2] * M_PI / 180.0); // 从b系到E系的欧拉角
	Eigen::Vector3d velocity_b = carBackRFUSpeed; // (carBackRFUSpeed[0], carBackRFUSpeed[1], carBackRFUSpeed[2]);   // b系下的速度
	Eigen::Vector3d velocity_e;           // e系下的速度
	// 计算旋转矩阵 Z-X-Y
	Eigen::Matrix3d rotation_matrix_b2e;  // 从b系到E系的旋转矩阵
	rotation_matrix_b2e = Eigen::AngleAxisd(euler_angle_b2e[1], Eigen::Vector3d::UnitY()).toRotationMatrix()
	                      * Eigen::AngleAxisd(euler_angle_b2e[0], Eigen::Vector3d::UnitX()).toRotationMatrix()
	                      * Eigen::AngleAxisd(euler_angle_b2e[2], Eigen::Vector3d::UnitZ()).toRotationMatrix();//npos TODO 根据红旗2坐标更改
						  
	// 将b系下速度转到e系（右前上）
	velocity_e = rotation_matrix_b2e * velocity_b;
	// 给出车辆运动速度（理论上应该有从b系到车辆坐标系的转换过程，但是这里没有精确的标定值，暂时忽略）
	
	std::vector<double> infoInENU{velocity_e[0], velocity_e[1], velocity_e[2]};
	
	return std::move(infoInENU);
}

/***
 * 车后轴右前上坐标系的Y轴正方向顺时针0~360度转到正北方向顺时针0~360
 * @param objectHeadingDegree
 * @param carHeadingDegree：从GPS得到的车辆航向角
 * @return
 */
float SensorAxisTransformation::CarBackRFUHeading2NorthClockwise(const float& objectHeadingDegree, const float& carHeadingDegree){
	float objectAngleDegreeInNorth_Clockwise = objectHeadingDegree + carHeadingDegree;
	objectAngleDegreeInNorth_Clockwise = objectAngleDegreeInNorth_Clockwise >= 360 ? objectAngleDegreeInNorth_Clockwise - 360:objectAngleDegreeInNorth_Clockwise;
	objectAngleDegreeInNorth_Clockwise = objectAngleDegreeInNorth_Clockwise < 0? objectAngleDegreeInNorth_Clockwise + 360:objectAngleDegreeInNorth_Clockwise;
	return objectAngleDegreeInNorth_Clockwise;
}

/***
 *
 * @param objectAngleDegreeInNorth_Clockwise：目标在正北方向顺时针0~360转到车后轴右前上坐标系的Y轴正方向顺时针0~360度
 * @param carHeadingDegree：从GPS得到的车辆航向角
 * @return
 */
float SensorAxisTransformation::NorthClockwise2CarBackRFU(const float& objectAngleDegreeInNorth_Clockwise, const float& carHeadingDegree){
	float objectAngleDegreeInCarBackRFU_Clockwise = (objectAngleDegreeInNorth_Clockwise - carHeadingDegree);
	objectAngleDegreeInCarBackRFU_Clockwise = objectAngleDegreeInCarBackRFU_Clockwise >= 360 ?
	                                          objectAngleDegreeInCarBackRFU_Clockwise - 360:
	                                          objectAngleDegreeInCarBackRFU_Clockwise;
	objectAngleDegreeInCarBackRFU_Clockwise = objectAngleDegreeInCarBackRFU_Clockwise < 0 ?
	                                          objectAngleDegreeInCarBackRFU_Clockwise + 360:
	                                          objectAngleDegreeInCarBackRFU_Clockwise;
	return objectAngleDegreeInCarBackRFU_Clockwise;
}


Eigen::Affine3f SensorAxisTransformation::getTransformation(
	const double& angleDegreex, const double& angleDegreey, const double& angleDegreez, 
	const double& tranlationx, const double& tranlationy, const double& tranlationz){
	wgs84_utm wgs84Utm;
	Eigen::AngleAxisf rotationVectorZ = Eigen::AngleAxisf(wgs84Utm.DegToRad(angleDegreez),Eigen::Vector3f::UnitZ());
	Eigen::AngleAxisf rotationVectorY = Eigen::AngleAxisf(wgs84Utm.DegToRad(angleDegreey),Eigen::Vector3f::UnitY());
	Eigen::AngleAxisf rotationVectorX = Eigen::AngleAxisf(wgs84Utm.DegToRad(angleDegreex),Eigen::Vector3f::UnitX());
	Eigen::Matrix3f rotationFrom = rotationVectorX.matrix() * rotationVectorY.matrix() * rotationVectorZ.matrix();
	Eigen::Affine3f transformation(rotationFrom);
	transformation.translation() << tranlationx, tranlationy, tranlationz;
	return std::move(transformation);
}