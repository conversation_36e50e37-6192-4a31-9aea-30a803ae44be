/********************************************************************************
* @author: shuang<PERSON>n han
* @date: 2022/9/13 下午7:18
* @version: 1.0
* @description:
********************************************************************************/

//#pragma once
#ifndef RICHARDPROJECT_WGS84_UTM_H
#define RICHARDPROJECT_WGS84_UTM_H

#include <iostream>
#include <cmath>
#include <vector>

//* 使用方法
//1.经纬度转UTM
// tagUTMCorr utm;
// LatLonToUTMXY(40.05169478 / 180 * M_PI, 116.28698024 / 180 * M_PI, utm);
// std::cerr << "utm.x utm.y =" << std::setprecision(18) << utm.x << " " << utm.y << std::endl;
//2.UTM转经纬度
// WGS84Corr lla;
// UTMXYToLatLon(utm.x, utm.y, 50, false, lla);
// std::cerr << "log lat =" << lla.log/M_PI*180 << " " << lla.lat/M_PI*180 << std::endl;

const double R_e = 6378137.0;       // 椭球体长半轴
const double R_f = 6356752.314245;  // 椭球体短半轴
const double e_1 = sqrt(pow(R_e, 2) - pow(R_f, 2)) / R_e;   // 第一偏心率

typedef struct Point2D
{
	double x;
	double y;
	double lat;
	double lon;
	int count;
	double azimuth;
} Point2D;


typedef struct tagUTMCorr // UTM坐标结构体（Universal Transverse Mercator，通用横轴墨卡托投影）
{
	double x;
	double y;
} UTMCoor;

typedef struct tagWGS84Corr // WGS-84坐标结构体
{
	double lat; //纬度
	double log; //经度
} WGS84Corr;

class wgs84_utm{
public:
	///* UTM坐标与WGS-84坐标系的相互转换
	///* 输入为坐标值，输出为结构体tagUTMCorr或tagWGS84Corr
	/*
	* DegToRad
	*
	* 角度转为弧度
	*
	*/
	 double DegToRad(double deg);//inline
	
	/*
	* RadToDeg
	*
	* 弧度转为角度
	*
	*/
	inline double RadToDeg(double rad);
	
	/*
	* ArcLengthOfMeridian
	*
	* 计算赤道（0°纬线）到输入纬度值的弧长
	*
	* Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
	* GPS: Theory and Practice, 3rd ed.  New York: Springer-Verlag Wien, 1994.
	*
	* Inputs:
	*     phi - Latitude，输入的纬度值，单位：弧度
	*
	* Returns:
	*     该纬度到赤道（0°纬线）的椭球面弧长，单位：m
	*
	*/
	double ArcLengthOfMeridian(double phi);
	
	/*
	* UTMCentralMeridian
	*
	* 计算给定UTM区域（Zone）中央经线的经度。（弧度）
	* UTM坐标系按经度将全球分为60个投影带
	*
	* Inputs:
	*     zone - 指定UTM区域编号, 范围 [1,60].
	*
	* Returns:
	*   给定UTM区域的中心经度，以弧度为单位；
	*   若输入超过有效范围（<0 or >60），返回0.
	*
	*/
	inline double UTMCentralMeridian(int zone);
	
	/*
	* FootpointLatitude
	*
	* 计算将TM坐标（Transverse Mercator，横轴墨卡托投影坐标，注意与UTM不同）转换为WGS-84坐标时使用的脚点纬度。
	*
	* Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
	*   GPS: Theory and Practice, 3rd ed.  New York: Springer-Verlag Wien, 1994.
	*
	* Inputs:
	*   y - TM的北向坐标，单位：m
	*
	* Returns:
	*   脚点纬度，单位：弧度
	*
	*/
	double FootpointLatitude(double y);
	
	/*
	* MapLatLonToXY
	*
	* 将WGS-84坐标转为TM坐标（Transverse Mercator，横轴墨卡托投影坐标，注意与UTM不同，TM与UTM之间相差一个比例因子）
	*
	* Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
	* GPS: Theory and Practice, 3rd ed.  New York: Springer-Verlag Wien, 1994.
	*
	* Inputs:
	*    phi - 该点的纬度，单位：弧度
	*    lambda - 该点经度，单位：弧度
	*    lambda0 - 该区域中央经线的经度，单位：弧度
	*
	* Outputs:
	*    xy - 该点的TM坐标，单位是m
	*
	* Returns:
	*    The function does not return a value.
	*
	*/
	void MapLatLonToXY(double phi, double lambda, double lambda0, UTMCoor &xy);
	
	/*
	* MapXYToLatLon
	*
	* 将TM坐标（Transverse Mercator，横轴墨卡托投影坐标，注意与UTM不同，TM与UTM之间相差一个比例因子）中的x,y坐标转换为WGS-84坐标
	*
	* Reference: Hoffmann-Wellenhof, B., Lichtenegger, H., and Collins, J.,
	*   GPS: Theory and Practice, 3rd ed.  New York: Springer-Verlag Wien, 1994.
	*
	* Inputs:
	*   x - 该点的TM坐标的x值，单位：m
	*   y - 该点的TM坐标的y值，单位：m
	*   lambda0 - 该区域中央经线的经度，单位：弧度
	*
	* Outputs:
	*   philambda - 该点的WGS-84坐标，单位：弧度
	*
	* Returns:
	*   The function does not return a value.
	*
	* Remarks:
	*   The local variables Nf, nuf2, tf, and tf2 serve the same purpose as
	*   N, nu2, t, and t2 in MapLatLonToXY, but they are computed with respect
	*   to the footpoint latitude phif.
	*
	*   x1frac, x2frac, x2poly, x3poly, etc. are to enhance readability and
	*   to optimize computations.
	*
	*/
	void MapXYToLatLon(double x, double y, double lambda0, WGS84Corr &philambda);
	
	/*
	* LatLonToUTMXY
	*
	* 将WGS-84坐标转换为UTM坐标（Universal Transverse Mercator，通用横轴墨卡托投影）中的x和y。
	*
	* Inputs:
	*   lat - 该点的纬度，单位：弧度
	*   lon - 该点的经度，单位：弧度
	*   zone - 该点所在的UTM区域（Zone），范围[1, 60]
	*   注意：***如果zone小于1或大于60，程序将根据lon的值确定合适的zone。
	*
	* Outputs:
	*   xy - 该点的UTM坐标
	*
	* Returns:
	*   void
	*
	*/
	void LatLonToUTMXY(double lat, double lon, UTMCoor &xy);
	

	/***
	 * 将WGS-84坐标转换为局部ENU坐标中的x和y。
	 * @param selfCarLonDegree 自车的经度，单位：度
	 * @param selfCarLatDegree 自车的纬度，单位：度
	 * @param objectLonDegree 目标的经度，单位：度
	 * @param objectLatDegree 目标的纬度，单位：度
	 * @param selfCarHeadingDegree 自车正北夹角，单位：度
	 * @param carBackRFU_x  目标在车辆局部ENU坐标中的x和y
	 * @param carBackRFU_y 目标在车辆局部ENU坐标中的x和y
	 */
	void LatLonToLocalXY(const double& selfCarLonDegree, const double& selfCarLatDegree,
	                                const double& selfCarHeadingDegree,
	                                const double& objectLonDegree, const double& objectLatDegree,
	                                double& carBackRFU_x, double& carBackRFU_y);
	
	/*
	* UTMXYToLatLon
	*
	* 将UTM坐标系的x y转换为WGS-84坐标
	*
	* Inputs:
	*	x - 该点的UTM坐标x值，单位：m
	*	y - 该点的UTM坐标y值，单位：m
	*	zone - 该点所在的UTM区域（Zone）
	*	southhemi - True：该点在南半球
	*               false 北半球
	*
	* Outputs:
	*	latlon - 该点的WGS-84坐标  单位:弧度制
	*
	* Returns:
	*	The function does not return a value.
	*
	*/
	void UTMXYToLatLon(double x, double y, int zone, bool southhemi, WGS84Corr &latlon);
	
	void runLatLonToUTMXY(double lon,double lat,tagUTMCorr& utm);
	std::vector<double> llhDegree2UTM(const double& longitudeDegree,const double& latitudeDegree);
	std::vector<double> getUTMPosition(const double& lontitudeDegree, const double& latitudeDegree, const double& altitudeDegree);
	int getUTMCode(double lontitudeDegree);
	void utm2LLADegree(double utm_x, double utm_y, int zone, bool southhemi, std::vector<double>& v_lonlat);
	
};//namespace wgs84_utm

#endif //RICHARDPROJECT_WGS84_UTM_H
