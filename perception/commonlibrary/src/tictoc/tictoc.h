/********************************************************************************
* @author: shuangquan han
* @date: 2022/8/11 下午4:52
* @version: 1.0
* @description: 
********************************************************************************/


#ifndef SRC_TICTOC_H
#define SRC_TICTOC_H

#pragma once

#include <iostream>
#include <ctime>
#include <cstdlib>
#include <chrono>

//TicToc ticToc;
//ticToc.toc();
//cout<<"fusion用时："<<endl;
//ticToc.toc();

class TicToc
{
public:
	TicToc()
	{
		tic();
	}
	
	void tic()
	{
		start = std::chrono::system_clock::now();
	}
	
	double toc()
	{
		end = std::chrono::system_clock::now();
		std::chrono::duration<double> elapsed_seconds = end - start;
		//std::cout<<"\tuse time(ms): "<<elapsed_seconds.count() * 1000<<std::endl;
		return elapsed_seconds.count() * 1000;
	}

private:
	std::chrono::time_point<std::chrono::system_clock> start, end;
};




#endif //SRC_TICTOC_H
