#ifndef __FILEWRITER__
#define __FILEWRITER__

#include <iostream>
#include <fstream>
#include <string>
#include <boost/make_shared.hpp>

#include "../configManager/configManager.h"

using namespace std;

class FileWriter{
    public:
        FileWriter(const boost::shared_ptr<ConfigManager::ConfigManager>& pConfigManager);
        ~FileWriter();
        void createFile(const std::string& fileName);
        void writeHeader(const std::string& dataHeader);
        void writeData(const std::string& data);
        
    private:
        boost::shared_ptr<ConfigManager::ConfigManager> m_pConfigManager;
        fstream m_file;
        std::string m_saveFilePath;
};


#endif // __FILEWRITER__