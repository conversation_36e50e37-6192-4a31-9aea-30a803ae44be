/*
 * @Description  : 
 * @Version      : V1.0.0
 * <AUTHOR> hanshuangquan <EMAIL>
 * @Date         : 2024-01-26 16:13:12
 * @LastEditors: hanshuangquan <EMAIL>
 * @LastEditTime: 2025-08-28 16:06:17
 * @FilePath: /autodrivingVersionTest/src/perception/commonlibrary/src/configManager/configManager.cpp
 * Copyright 2024 Marvin, All Rights Reserved. 
 * 2024-01-26 16:13:12
 */
#include "configManager.h"
#include "../common.h"
#include "../logger/logger.h"
#include <sstream>

namespace ConfigManager{


ConfigManager::ConfigManager(const std::string yamlPath){
    try{
        m_configManager = YAML::LoadFile(yamlPath);
        std::cout << "read yaml config success." << std::endl;
        readParam();
    }
    catch (YAML::BadFile &e){
        std::cout << FRED("Error: load config paramater error!" )<< std::endl;
    }
}

ConfigManager::~ConfigManager()
{
}

#ifdef ROS1_FOUND

void ConfigManager::readParam(){
    m_hdMapName = "HDMap-ruanjianyuan.png";
    m_hdMapLongitude = 116.279401146;
	m_hdMapLatitude = 40.053995788;
    m_cityUTMCode = 50;
    m_carNumber = 2;
    
    m_isUseHDMap = false;
    m_isUseRosBag = false;
	m_isSimulateMode = false;
	m_bdebug = 0;
	m_radarAsObjectDistance = 0;
	m_isSaveObjectInfoCSV = false;
	m_isUseRadarObjects = false;
	m_isEvaluateTracking = false;
	m_isUseRadarObjects = false;
    

	m_hdMapName = m_configManager["hdMapName"].as<std::string>();
    m_hdMapLongitude = m_configManager["hdMapLongitude"].as<double>();
    m_hdMapLatitude = m_configManager["hdMapLatitude"].as<double>();
    m_cityUTMCode = m_configManager["cityUTMCode"].as<int>();
    m_carNumber = m_configManager["carNumber"].as<int>();
    m_isUseHDMap = m_configManager["isUseHDMap"].as<bool>();

    m_isUseRosBag = m_configManager["isUseRosBag"].as<bool>();
    m_isSimulateMode = m_configManager["isSimulateMode"].as<bool>();
	m_bdebug = m_configManager["bdebug"].as<int>();
    m_radarAsObjectDistance = m_configManager["radarAsObjectDistance"].as<float>();
    m_front2BackDistance = m_configManager["front2BackDistance"].as<float>();
    m_isSaveObjectInfoCSV = m_configManager["isSaveObjectInfoCSV"].as<bool>();
    m_isEvaluateTracking = m_configManager["isUseRosBag"].as<bool>();
    m_isEvaluateTracking = m_configManager["isEvaluateTracking"].as<bool>();

    m_debugID = m_configManager["fusiontracking"]["debugID"].as<int>();
    m_isUseRadarObjects = m_configManager["fusiontracking"]["isUseRadarObjects"].as<bool>();
    m_isSaveTimeUseFile = m_configManager["fusiontracking"]["isSaveTimeUse"].as<bool>();
    m_saveTimeUseFilePath = m_configManager["fusiontracking"]["timeUseFilePath"].as<std::string>();
    m_logFilePath = m_configManager["log"]["filePath"].as<std::string>();
    m_logFileName = m_configManager["log"]["fileName"].as<std::string>();
    m_logOutputType = m_configManager["log"]["outputType"].as<int>();
    m_logFileMaxSize = m_configManager["log"]["fileMaxSize"].as<int>();
    m_logFileMaxNum = m_configManager["log"]["fileMaxNum"].as<int>();

    m_stationRange = m_configManager["stationRangeTest"].as<std::vector<std::vector<double>>>();

    if(carNumberMap.find(m_carNumber) != carNumberMap.end()){
		m_carName = carNumberMap.at(m_carNumber);
	}
	else{
		m_carName = carNumberMap.at(0);
	}

}
#endif


#ifdef ROS2_FOUND
void ConfigManager::readParam(){
    m_hdMapName = "HDMap-ruanjianyuan.png";
    m_hdMapLongitude = 116.279401146;
	m_hdMapLatitude = 40.053995788;
    m_cityUTMCode = 50;
    m_carNumber = 2;
    
    m_isUseHDMap = false;
    m_isUseRosBag = false;
	m_isSimulateMode = false;
	m_bdebug = 0;
	m_radarAsObjectDistance = 0;
	m_isSaveObjectInfoCSV = false;
	m_isUseRadarObjects = false;
	m_isEvaluateTracking = false;
	m_isUseRadarObjects = false;

    // 获取 ROS2 参数节点
    YAML::Node params = m_configManager["fusiontracking"]["ros__parameters"];
    m_isUseRosBag = params["isUseRosBag"].as<bool>();
    m_cityUTMCode = params["cityUTMCode"].as<int>();
    m_carNumber = params["carNumber"].as<int>();
    m_isUseHDMap = params["isUseHDMap"].as<bool>();
	m_hdMapName = params["hdMapName"].as<std::string>();
    m_hdMapLongitude = params["hdMapLongitude"].as<double>();
    m_hdMapLatitude = params["hdMapLatitude"].as<double>();
    m_front2BackDistance = params["front2BackDistance"].as<float>();
    m_isSaveObjectInfoCSV = params["isSaveObjectInfoCSV"].as<bool>();
    m_isEvaluateTracking = params["isEvaluateTracking"].as<bool>();
    m_carCenter2CarBackDistance = params["carCenter2CarBackDistance"].as<float>();

    
    m_isSimulateMode = params["isSimulateMode"].as<bool>();
    m_bdebug = params["bdebug"].as<int>();

    m_radarAsObjectDistance = params["radarAsObjectDistance"].as<float>();

    // fusiontracking
    m_isUseRadarObjects = params["fusiontracking"]["isUseRadarObjects"].as<bool>();
    m_debugID = params["fusiontracking"]["debugID"].as<int>();
    m_isSaveTimeUseFile = params["fusiontracking"]["isSaveTimeUse"].as<bool>();
    m_saveTimeUseFilePath = params["fusiontracking"]["timeUseFilePath"].as<std::string>();

    // log
    YAML::Node logParams = params["log"];
    m_logFilePath = logParams["filePath"].as<std::string>();
    m_logFileName = logParams["fileName"].as<std::string>();
    m_logOutputType = logParams["outputType"].as<int>();
    m_logFileMaxSize = logParams["fileMaxSize"].as<int>();
    m_logFileMaxNum = logParams["fileMaxNum"].as<int>();

    // Parse stationRangeTest from string format for ROS2 compatibility
    if (params["stationRangeTest"]) {
        try {
            std::string stationRangeStr = params["stationRangeTest"].as<std::string>();
            m_stationRange.clear();

            std::cout << "Parsing stationRangeTest string: " << stationRangeStr << std::endl;

            // Split by semicolon to get each coordinate pair
            std::stringstream ss(stationRangeStr);
            std::string pair;
            while (std::getline(ss, pair, ';')) {
                std::vector<double> coords;
                std::stringstream pairSS(pair);
                std::string coord;

                // Split by comma to get lon,lat
                while (std::getline(pairSS, coord, ',')) {
                    coords.push_back(std::stod(coord));
                }

                if (coords.size() == 2) {
                    m_stationRange.push_back(coords);
                    std::cout << "Added coordinate pair: [" << coords[0] << ", " << coords[1] << "]" << std::endl;
                }
            }
            std::cout << "Total station range pairs loaded: " << m_stationRange.size() << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Error parsing stationRangeTest string: " << e.what() << std::endl;
            // Fallback to original format if available
            try {
                m_stationRange = params["stationRangeTest"].as<std::vector<std::vector<double>>>();
            } catch (const std::exception& e2) {
                std::cout << "Error parsing stationRangeTest as array: " << e2.what() << std::endl;
            }
        }
    }

    if(carNumberMap.find(m_carNumber) != carNumberMap.end()){
		m_carName = carNumberMap.at(m_carNumber);
	}
	else{
		m_carName = carNumberMap.at(0);
	}

}
#endif

void ConfigManager::printParams(std::shared_ptr<spdlog::logger>& pLogger){
    pLogger->info("params................................");
    pLogger->info("carNumber:  {} ",m_carNumber);
    pLogger->info("carName:   {}",m_carName);
    pLogger->info("isUseRosBag:   {}",  m_isUseRosBag);
    pLogger->info("debugID:   {}",  m_debugID);
    pLogger->info("isUseRadarObjects:   {}",  m_isUseRadarObjects);
    pLogger->info("isEvaluateTracking:   {}",  m_isEvaluateTracking);
    pLogger->info("isUseHDMap:   {}",  m_isUseHDMap);
    pLogger->info("hdMapName:   {}", m_hdMapName);
    pLogger->info("hdMapLongitude:   {:.7f}",  m_hdMapLongitude);
    pLogger->info("hdMapLatitude:   {:.7f}",  m_hdMapLatitude);
    pLogger->info("cityUTMCode:   {}",  m_cityUTMCode);
    pLogger->info("isSimulateMode:   {}",  m_isSimulateMode);
    pLogger->info("bdebug:   {}",  m_bdebug);
    pLogger->info("radarAsObjectDistance:   {}",  m_radarAsObjectDistance);
    pLogger->info("front2BackDistance:   {}",  m_front2BackDistance);
    pLogger->info("isSaveObjectInfoCSV:   {}",  m_isSaveObjectInfoCSV);
    pLogger->info("logFilePath:   {}",  m_logFilePath);
    pLogger->info("logFileName:   {}",  m_logFileName);
    pLogger->info("logOutputType:   {}",  m_logOutputType);
    pLogger->info("logFileMaxSize:   {}",  m_logFileMaxSize);
    pLogger->info("logFileMaxNum:   {}",  m_logFileMaxNum);
    pLogger->info("params end................................\n");
}


}