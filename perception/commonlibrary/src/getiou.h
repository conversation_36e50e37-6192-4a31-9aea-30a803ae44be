//
// Created by wanji on 2022/5/6.
//

#ifndef MAIN_CPP_GETIOU_H
#define MAIN_CPP_GETIOU_H

#include <iostream>
#include <vector>
#include <eigen3/Eigen/Dense>
#include "opencv2/highgui/highgui.hpp"

using namespace std;
//默认BOX,左右角点的定义:（x0,y0）(x1,y1)
struct Box
{
	double x0;
	double y0;
	double x1;
	double y1;
	
};

float CuboidIoU(const vector<float>& predictBox,const vector<float>& detectedBox);
//float CuboidIoU(const Eigen::MatrixXd &truth_poses, const Eigen::MatrixXd &landmark_poses);//compute iou 3-d
float calcIOU(cv::RotatedRect rect1, cv::RotatedRect rect2);
double InterSection_2D(const std::vector<cv::Point2f> &vpBoxA, const std::vector<cv::Point2f> &vpBoxB);

double IoU_compute(Box &a,Box &b,bool GIoU=false,bool DIoU=false, bool CIoU=false,bool EIoU=false,double eps=1e-9);
float runIOUCompute(const Eigen::MatrixXd &truth_poses, const Eigen::MatrixXd &landmark_poses);
#endif //MAIN_CPP_GETIOU_H
